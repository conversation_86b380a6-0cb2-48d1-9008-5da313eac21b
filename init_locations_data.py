#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت تهيئة البيانات الأولية للمواقع
"""

import sqlite3
import os
from datetime import datetime

def init_locations_data():
    """تهيئة البيانات الأولية للمواقع"""
    
    # الاتصال بقاعدة بيانات المواقع
    conn = sqlite3.connect('locations.db')
    cursor = conn.cursor()
    
    # إنشاء الجداول إذا لم تكن موجودة
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS locations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            serial_number TEXT UNIQUE NOT NULL,
            type TEXT NOT NULL DEFAULT 'أمني',
            status TEXT NOT NULL DEFAULT 'نشط',
            coordinates TEXT,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS location_equipment (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            location_id INTEGER NOT NULL,
            equipment_name TEXT NOT NULL,
            equipment_type TEXT NOT NULL,
            serial_number TEXT,
            quantity INTEGER DEFAULT 1,
            status TEXT DEFAULT 'نشط',
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE CASCADE
        )
    ''')
    
    # البيانات الأولية للمواقع
    default_locations = [
        {
            'name': 'البوابة الرئيسية الشمالية',
            'serial_number': 'LOC-001',
            'type': 'أمني',
            'status': 'نشط',
            'coordinates': '24.7136° N, 46.6753° E',
            'description': 'البوابة الرئيسية للمنطقة الشمالية - نقطة دخول رئيسية'
        },
        {
            'name': 'البوابة الجنوبية',
            'serial_number': 'LOC-002',
            'type': 'أمني',
            'status': 'نشط',
            'coordinates': '24.7100° N, 46.6750° E',
            'description': 'البوابة الجنوبية للمنطقة - نقطة دخول ثانوية'
        },
        {
            'name': 'برج المراقبة الشرقي',
            'serial_number': 'LOC-003',
            'type': 'مراقبة',
            'status': 'نشط',
            'coordinates': '24.7140° N, 46.6780° E',
            'description': 'برج مراقبة استراتيجي يطل على المنطقة الشرقية'
        },
        {
            'name': 'برج المراقبة الغربي',
            'serial_number': 'LOC-004',
            'type': 'مراقبة',
            'status': 'نشط',
            'coordinates': '24.7130° N, 46.6720° E',
            'description': 'برج مراقبة يطل على المنطقة الغربية'
        },
        {
            'name': 'المبنى الإداري الرئيسي',
            'serial_number': 'LOC-005',
            'type': 'إداري',
            'status': 'نشط',
            'coordinates': '24.7120° N, 46.6750° E',
            'description': 'المبنى الإداري الرئيسي - مكاتب القيادة والإدارة'
        },
        {
            'name': 'موقف السيارات الرئيسي',
            'serial_number': 'LOC-006',
            'type': 'حراسة',
            'status': 'نشط',
            'coordinates': '24.7110° N, 46.6740° E',
            'description': 'موقف السيارات الرئيسي - يتطلب حراسة مستمرة'
        },
        {
            'name': 'المستودع المركزي',
            'serial_number': 'LOC-007',
            'type': 'تخزين',
            'status': 'نشط',
            'coordinates': '24.7125° N, 46.6765° E',
            'description': 'المستودع المركزي للمعدات والإمدادات'
        },
        {
            'name': 'ساحة التدريب',
            'serial_number': 'LOC-008',
            'type': 'تدريب',
            'status': 'نشط',
            'coordinates': '24.7150° N, 46.6770° E',
            'description': 'ساحة التدريب العسكري والتمارين'
        },
        {
            'name': 'المطبخ المركزي',
            'serial_number': 'LOC-009',
            'type': 'خدمي',
            'status': 'نشط',
            'coordinates': '24.7115° N, 46.6755° E',
            'description': 'المطبخ المركزي لإعداد الوجبات'
        },
        {
            'name': 'العيادة الطبية',
            'serial_number': 'LOC-010',
            'type': 'طبي',
            'status': 'نشط',
            'coordinates': '24.7118° N, 46.6748° E',
            'description': 'العيادة الطبية للرعاية الصحية الأولية'
        }
    ]
    
    # إدراج البيانات
    for location in default_locations:
        # التحقق من عدم وجود الموقع مسبقاً
        cursor.execute('SELECT id FROM locations WHERE serial_number = ?', (location['serial_number'],))
        if not cursor.fetchone():
            cursor.execute('''
                INSERT INTO locations (name, serial_number, type, status, coordinates, description, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                location['name'],
                location['serial_number'],
                location['type'],
                location['status'],
                location['coordinates'],
                location['description'],
                datetime.now().isoformat(),
                datetime.now().isoformat()
            ))
            print(f"✅ تم إضافة موقع: {location['name']}")
        else:
            print(f"ℹ️  موقع {location['name']} موجود بالفعل")
    
    # إضافة بعض العهد النموذجية للمواقع
    sample_equipment = [
        {'location_serial': 'LOC-001', 'equipment_name': 'كاميرا مراقبة', 'equipment_type': 'أمني', 'serial_number': 'CAM-001', 'quantity': 2},
        {'location_serial': 'LOC-001', 'equipment_name': 'جهاز اتصال لاسلكي', 'equipment_type': 'اتصالات', 'serial_number': 'RADIO-001', 'quantity': 1},
        {'location_serial': 'LOC-003', 'equipment_name': 'منظار مراقبة', 'equipment_type': 'مراقبة', 'serial_number': 'BIN-001', 'quantity': 1},
        {'location_serial': 'LOC-003', 'equipment_name': 'كاميرا مراقبة ليلية', 'equipment_type': 'أمني', 'serial_number': 'NIGHTCAM-001', 'quantity': 1},
        {'location_serial': 'LOC-005', 'equipment_name': 'حاسوب مكتبي', 'equipment_type': 'إداري', 'serial_number': 'PC-001', 'quantity': 5},
        {'location_serial': 'LOC-005', 'equipment_name': 'طابعة ليزر', 'equipment_type': 'إداري', 'serial_number': 'PRINTER-001', 'quantity': 2},
    ]
    
    for equipment in sample_equipment:
        # الحصول على معرف الموقع
        cursor.execute('SELECT id FROM locations WHERE serial_number = ?', (equipment['location_serial'],))
        location_row = cursor.fetchone()
        if location_row:
            location_id = location_row[0]
            
            # التحقق من عدم وجود العهدة مسبقاً
            cursor.execute('SELECT id FROM location_equipment WHERE location_id = ? AND serial_number = ?', 
                         (location_id, equipment['serial_number']))
            if not cursor.fetchone():
                cursor.execute('''
                    INSERT INTO location_equipment (location_id, equipment_name, equipment_type, serial_number, quantity, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    location_id,
                    equipment['equipment_name'],
                    equipment['equipment_type'],
                    equipment['serial_number'],
                    equipment['quantity'],
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                ))
                print(f"✅ تم إضافة عهدة: {equipment['equipment_name']} للموقع {equipment['location_serial']}")
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print(f"\n🎉 تم تهيئة قاعدة بيانات المواقع بنجاح!")
    print(f"📊 تم إضافة {len(default_locations)} موقع")
    print(f"📦 تم إضافة {len(sample_equipment)} عهدة نموذجية")

if __name__ == '__main__':
    init_locations_data()
