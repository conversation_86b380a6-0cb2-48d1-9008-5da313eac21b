#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار عرض الأفراد في صفحة الموقع
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from locations import get_location_by_id

def test_location_view():
    """اختبار عرض الأفراد في صفحة الموقع"""
    
    with app.app_context():
        print("🔍 اختبار عرض الأفراد في صفحة الموقع...")
        print("=" * 50)
        
        # اختبار الموقع الذي تم ربط فرد به (الكاجيما - ID: 48)
        location_id = 48
        location = get_location_by_id(location_id)
        
        if location:
            print(f"📍 الموقع: {location['name']} (ID: {location['id']})")
            print(f"📊 عدد العهد: {len(location.get('equipment', []))}")
            
            # فحص الأفراد
            personnel = location.get('personnel', [])
            print(f"👥 عدد الأفراد: {len(personnel)}")
            
            if personnel:
                print("\n👤 الأفراد المرتبطين:")
                for person in personnel:
                    print(f"  - الاسم: {person['name']}")
                    print(f"    الرقم العسكري: {person['personnel_id']}")
                    print(f"    رقم الهوية: {person['phone']}")
                    print(f"    الحالة: {person['status']}")
                    print(f"    نوع الوردية: {person['shift_type']}")
                    print(f"    الملاحظات: {person['notes']}")
                    print()
            else:
                print("❌ لا يوجد أفراد مرتبطين بالموقع")
                
                # التحقق من قاعدة البيانات مباشرة
                from locations import get_db_connection
                conn = get_db_connection()
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT lp.*, p.name, p.personnel_id, p.phone 
                    FROM location_personnel lp
                    JOIN personnel p ON lp.personnel_id = p.id
                    WHERE lp.location_id = ? AND lp.is_active = 1
                """, (location_id,))
                
                db_personnel = cursor.fetchall()
                conn.close()
                
                if db_personnel:
                    print(f"⚠️ وجدت {len(db_personnel)} أفراد في قاعدة البيانات:")
                    for person in db_personnel:
                        print(f"  - {person['name']} ({person['personnel_id']})")
                    print("🔧 المشكلة في دالة get_location_personnel")
                else:
                    print("❌ لا توجد أفراد في قاعدة البيانات أيضاً")
        else:
            print(f"❌ الموقع {location_id} غير موجود")

if __name__ == "__main__":
    test_location_view()
