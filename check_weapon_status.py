#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import os

def check_weapon_status():
    """فحص شامل لحالة السلاح والسند"""
    
    try:
        conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
        cursor = conn.cursor()
        
        # السلاح المحدد من الصورة
        serial = 'G-K4S1B-3480-0.091001634'
        
        print("🔍 فحص شامل لحالة السلاح والسند")
        print("=" * 50)
        
        # 1. فحص السلاح في قاعدة البيانات
        cursor.execute('''
            SELECT id, serial_number, name, weapon_document, updated_at
            FROM weapons 
            WHERE serial_number = %s
        ''', (serial,))
        
        weapon = cursor.fetchone()
        if weapon:
            weapon_id, serial_num, name, doc_path, updated_at = weapon
            print(f"✅ السلاح موجود في قاعدة البيانات:")
            print(f"   - المعرف: {weapon_id}")
            print(f"   - الرقم التسلسلي: {serial_num}")
            print(f"   - الاسم: {name}")
            print(f"   - مسار السند: {doc_path}")
            print(f"   - آخر تحديث: {updated_at}")
            
            # 2. فحص وجود الملف
            if doc_path:
                print(f"\n📄 فحص ملف السند:")
                if os.path.exists(doc_path):
                    file_size = os.path.getsize(doc_path)
                    print(f"   ✅ الملف موجود: {doc_path}")
                    print(f"   📏 حجم الملف: {file_size:,} بايت ({file_size/1024/1024:.2f} ميجابايت)")
                    
                    # فحص صيغة الملف
                    file_extension = doc_path.split('.')[-1].lower()
                    print(f"   📋 نوع الملف: {file_extension}")
                    
                    # فحص الصلاحيات
                    try:
                        with open(doc_path, 'rb') as f:
                            f.read(1)
                        print(f"   ✅ يمكن قراءة الملف")
                    except Exception as e:
                        print(f"   ❌ خطأ في قراءة الملف: {e}")
                else:
                    print(f"   ❌ الملف غير موجود: {doc_path}")
            else:
                print(f"\n❌ لا يوجد مسار سند مرتبط بالسلاح")
            
            # 3. فحص الملفات المطابقة في المجلد
            print(f"\n📁 فحص الملفات في المجلد:")
            documents_dir = 'static/uploads/weapon_documents'
            if os.path.exists(documents_dir):
                files = os.listdir(documents_dir)
                matching_files = [f for f in files if '3480' in f and 'G-K4S1B' in f]
                
                print(f"   📊 إجمالي الملفات في المجلد: {len(files)}")
                print(f"   🎯 الملفات المطابقة للسلاح: {len(matching_files)}")
                
                for file in matching_files:
                    file_path = os.path.join(documents_dir, file)
                    file_size = os.path.getsize(file_path)
                    print(f"     - {file} ({file_size:,} بايت)")
            else:
                print(f"   ❌ مجلد السندات غير موجود: {documents_dir}")
            
            # 4. اختبار URL التحميل
            print(f"\n🌐 معلومات URL:")
            print(f"   - معرف السلاح: {weapon_id}")
            print(f"   - URL التحميل: /weapons/{weapon_id}/download-document")
            
            # 5. فحص الصلاحيات والمستودع
            cursor.execute('''
                SELECT w.name as warehouse_name, w.id as warehouse_id
                FROM weapons wp
                JOIN warehouses w ON wp.warehouse_id = w.id
                WHERE wp.id = %s
            ''', (weapon_id,))
            
            warehouse = cursor.fetchone()
            if warehouse:
                warehouse_name, warehouse_id = warehouse
                print(f"\n🏢 معلومات المستودع:")
                print(f"   - اسم المستودع: {warehouse_name}")
                print(f"   - معرف المستودع: {warehouse_id}")
            
        else:
            print(f"❌ لم يتم العثور على السلاح بالرقم التسلسلي: {serial}")
        
        # 6. إحصائيات عامة
        print(f"\n📊 إحصائيات عامة:")
        cursor.execute('SELECT COUNT(*) FROM weapons')
        total_weapons = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM weapons WHERE weapon_document IS NOT NULL')
        weapons_with_docs = cursor.fetchone()[0]
        
        print(f"   - إجمالي الأسلحة: {total_weapons}")
        print(f"   - الأسلحة التي لها سندات: {weapons_with_docs}")
        print(f"   - النسبة المئوية: {(weapons_with_docs/total_weapons*100):.1f}%")
        
        conn.close()
        
        print("\n" + "=" * 50)
        print("✅ انتهى الفحص")
        
        # نصائح للحل
        print(f"\n💡 نصائح:")
        print(f"   1. تأكد من إعادة تشغيل التطبيق لتحديث البيانات")
        print(f"   2. امسح الكاش في المتصفح (Ctrl+F5)")
        print(f"   3. تحقق من أن المستخدم له صلاحية الوصول للمستودع")
        print(f"   4. تأكد من أن Flask يقرأ البيانات الجديدة من قاعدة البيانات")
        
    except Exception as e:
        print(f"❌ خطأ في الفحص: {str(e)}")

if __name__ == '__main__':
    check_weapon_status()
