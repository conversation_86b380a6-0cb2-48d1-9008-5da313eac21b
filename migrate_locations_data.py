#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت نقل بيانات المواقع من warehouse.db إلى locations.db
"""

import sqlite3
from datetime import datetime

def migrate_locations_data():
    """نقل بيانات المواقع من warehouse.db إلى locations.db"""
    
    print("🔄 بدء نقل بيانات المواقع...")
    
    # الاتصال بقاعدة البيانات المصدر (warehouse.db)
    source_conn = sqlite3.connect('warehouse.db')
    source_cursor = source_conn.cursor()
    
    # الاتصال بقاعدة البيانات الهدف (locations.db)
    target_conn = sqlite3.connect('locations.db')
    target_cursor = target_conn.cursor()
    
    try:
        # إنشاء الجداول في قاعدة البيانات الهدف إذا لم تكن موجودة
        target_cursor.execute('''
            CREATE TABLE IF NOT EXISTS locations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                serial_number TEXT UNIQUE NOT NULL,
                type TEXT NOT NULL DEFAULT 'أمني',
                status TEXT NOT NULL DEFAULT 'نشط',
                coordinates TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER
            )
        ''')
        
        target_cursor.execute('''
            CREATE TABLE IF NOT EXISTS location_equipment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                location_id INTEGER NOT NULL,
                equipment_name TEXT NOT NULL,
                equipment_type TEXT NOT NULL,
                serial_number TEXT,
                quantity INTEGER DEFAULT 1,
                status TEXT DEFAULT 'نشط',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (location_id) REFERENCES locations (id) ON DELETE CASCADE
            )
        ''')
        
        # مسح البيانات الموجودة في locations.db أولاً
        target_cursor.execute('DELETE FROM location_equipment')
        target_cursor.execute('DELETE FROM locations')
        print("✅ تم مسح البيانات القديمة")
        
        # جلب جميع المواقع من warehouse.db
        source_cursor.execute('SELECT * FROM locations ORDER BY created_at')
        source_locations = source_cursor.fetchall()
        
        # الحصول على أسماء الأعمدة
        source_cursor.execute('PRAGMA table_info(locations)')
        columns_info = source_cursor.fetchall()
        column_names = [col[1] for col in columns_info]
        
        print(f"📊 تم العثور على {len(source_locations)} موقع في warehouse.db")
        print(f"📋 أعمدة الجدول: {', '.join(column_names)}")
        
        # نقل المواقع
        for location_row in source_locations:
            location_dict = dict(zip(column_names, location_row))
            
            # إدراج الموقع في قاعدة البيانات الهدف
            target_cursor.execute('''
                INSERT INTO locations (name, serial_number, type, status, coordinates, description, created_at, updated_at, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                location_dict.get('name', ''),
                location_dict.get('serial_number', ''),
                location_dict.get('type', 'أمني'),
                location_dict.get('status', 'نشط'),
                location_dict.get('coordinates', ''),
                location_dict.get('description', ''),
                location_dict.get('created_at', datetime.now().isoformat()),
                location_dict.get('updated_at', datetime.now().isoformat()),
                location_dict.get('created_by', None)
            ))
            
            print(f"✅ تم نقل موقع: {location_dict.get('name', 'غير محدد')} ({location_dict.get('serial_number', 'غير محدد')})")
        
        # جلب ونقل العهد المرتبطة بالمواقع
        source_cursor.execute('SELECT * FROM location_equipment ORDER BY created_at')
        source_equipment = source_cursor.fetchall()
        
        if source_equipment:
            # الحصول على أسماء أعمدة جدول العهد
            source_cursor.execute('PRAGMA table_info(location_equipment)')
            equipment_columns_info = source_cursor.fetchall()
            equipment_column_names = [col[1] for col in equipment_columns_info]
            
            print(f"📦 تم العثور على {len(source_equipment)} عهدة")
            
            # إنشاء خريطة لربط معرفات المواقع القديمة بالجديدة
            location_id_map = {}
            
            # جلب المواقع من قاعدة البيانات المصدر مع معرفاتها
            source_cursor.execute('SELECT id, serial_number FROM locations')
            source_location_ids = source_cursor.fetchall()
            
            # جلب المواقع من قاعدة البيانات الهدف مع معرفاتها
            target_cursor.execute('SELECT id, serial_number FROM locations')
            target_location_ids = target_cursor.fetchall()
            
            # إنشاء خريطة الربط
            for source_id, serial_number in source_location_ids:
                for target_id, target_serial in target_location_ids:
                    if serial_number == target_serial:
                        location_id_map[source_id] = target_id
                        break
            
            # نقل العهد
            for equipment_row in source_equipment:
                equipment_dict = dict(zip(equipment_column_names, equipment_row))
                old_location_id = equipment_dict.get('location_id')
                new_location_id = location_id_map.get(old_location_id)

                if new_location_id:
                    # إدراج العهدة مع الأعمدة المتاحة فقط
                    target_cursor.execute('''
                        INSERT INTO location_equipment (location_id, equipment_name, equipment_type, serial_number, quantity, notes, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        new_location_id,
                        equipment_dict.get('equipment_name', ''),
                        equipment_dict.get('equipment_type', ''),
                        equipment_dict.get('serial_number', ''),
                        equipment_dict.get('quantity', 1),
                        equipment_dict.get('notes', ''),
                        equipment_dict.get('created_at', datetime.now().isoformat()),
                        equipment_dict.get('updated_at', datetime.now().isoformat())
                    ))
                    
                    print(f"✅ تم نقل عهدة: {equipment_dict.get('equipment_name', 'غير محدد')}")
        
        # حفظ التغييرات
        target_conn.commit()
        
        # التحقق من النتائج
        target_cursor.execute('SELECT COUNT(*) FROM locations')
        locations_count = target_cursor.fetchone()[0]
        
        target_cursor.execute('SELECT COUNT(*) FROM location_equipment')
        equipment_count = target_cursor.fetchone()[0]
        
        print(f"\n🎉 تم نقل البيانات بنجاح!")
        print(f"📊 عدد المواقع المنقولة: {locations_count}")
        print(f"📦 عدد العهد المنقولة: {equipment_count}")
        
        # عرض المواقع المنقولة
        target_cursor.execute('SELECT name, serial_number, type FROM locations ORDER BY name')
        final_locations = target_cursor.fetchall()
        
        print(f"\n📋 المواقع المنقولة:")
        print("=" * 50)
        for i, (name, serial, loc_type) in enumerate(final_locations, 1):
            print(f"{i}. {name} ({serial}) - {loc_type}")
        
    except Exception as e:
        print(f"❌ خطأ في نقل البيانات: {str(e)}")
        target_conn.rollback()
    
    finally:
        source_conn.close()
        target_conn.close()

if __name__ == '__main__':
    migrate_locations_data()
