#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة جدولة النسخ الاحتياطي مباشرة
Direct Schedule Management

هذا الملف يتيح إدارة جدولات النسخ الاحتياطي مباشرة من قاعدة البيانات
بدون الحاجة لتشغيل الخادم
"""

import os
import sys
from datetime import datetime

# إضافة المسار الحالي للبحث عن الوحدات
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def show_schedules():
    """عرض جميع الجدولات"""
    
    print("📋 عرض جميع جدولات النسخ الاحتياطي:")
    print("=" * 60)
    
    try:
        from app import create_app
        from models import BackupSchedule
        from utils import format_datetime_12h, format_time_12h
        
        app = create_app()
        with app.app_context():
            schedules = BackupSchedule.query.all()
            
            if not schedules:
                print("📋 لا توجد جدولات في قاعدة البيانات")
                return []
            
            print(f"{'ID':<4} {'النوع':<8} {'نوع النسخة':<10} {'الوقت':<10} {'المستودع':<15} {'الحالة':<8} {'التشغيل التالي'}")
            print("-" * 80)
            
            for schedule in schedules:
                schedule_type_ar = {
                    'daily': 'يومي',
                    'weekly': 'أسبوعي', 
                    'monthly': 'شهري'
                }.get(schedule.schedule_type, schedule.schedule_type)
                
                backup_type_ar = {
                    'full': 'كامل',
                    'warehouse': 'مستودع'
                }.get(schedule.backup_type, schedule.backup_type)
                
                warehouse_name = schedule.warehouse.name if schedule.warehouse else "جميع المستودعات"
                status = "نشط" if schedule.is_active else "متوقف"
                
                time_str = format_time_12h(schedule.hour, schedule.minute)
                next_run = format_datetime_12h(schedule.next_run) if schedule.next_run else "-"
                
                print(f"{schedule.id:<4} {schedule_type_ar:<8} {backup_type_ar:<10} {time_str:<10} {warehouse_name:<15} {status:<8} {next_run}")
            
            return schedules
            
    except Exception as e:
        print(f"❌ خطأ في عرض الجدولات: {str(e)}")
        return []

def toggle_schedule(schedule_id):
    """تفعيل/إيقاف جدولة"""
    
    print(f"🔄 تبديل حالة الجدولة {schedule_id}:")
    print("=" * 40)
    
    try:
        from app import create_app
        from models import BackupSchedule
        from utils import calculate_next_run
        from db import db
        
        app = create_app()
        with app.app_context():
            schedule = BackupSchedule.query.get(schedule_id)
            
            if not schedule:
                print(f"❌ لا توجد جدولة بـ ID = {schedule_id}")
                return False
            
            old_status = "نشط" if schedule.is_active else "متوقف"
            
            # تبديل الحالة
            schedule.is_active = not schedule.is_active
            
            # إعادة حساب التشغيل التالي إذا تم التفعيل
            if schedule.is_active:
                schedule.next_run = calculate_next_run(
                    schedule.schedule_type,
                    schedule.hour,
                    schedule.minute,
                    schedule.day_of_week,
                    schedule.day_of_month
                )
            else:
                schedule.next_run = None
            
            db.session.commit()
            
            new_status = "نشط" if schedule.is_active else "متوقف"
            
            print(f"✅ تم تغيير حالة الجدولة من '{old_status}' إلى '{new_status}'")
            
            if schedule.is_active and schedule.next_run:
                from utils import format_datetime_12h
                print(f"📅 التشغيل التالي: {format_datetime_12h(schedule.next_run)}")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في تبديل حالة الجدولة: {str(e)}")
        return False

def delete_schedule(schedule_id):
    """حذف جدولة"""
    
    print(f"🗑️  حذف الجدولة {schedule_id}:")
    print("=" * 40)
    
    try:
        from app import create_app
        from models import BackupSchedule
        from db import db
        
        app = create_app()
        with app.app_context():
            schedule = BackupSchedule.query.get(schedule_id)
            
            if not schedule:
                print(f"❌ لا توجد جدولة بـ ID = {schedule_id}")
                return False
            
            # عرض تفاصيل الجدولة قبل الحذف
            schedule_type_ar = {
                'daily': 'يومي',
                'weekly': 'أسبوعي', 
                'monthly': 'شهري'
            }.get(schedule.schedule_type, schedule.schedule_type)
            
            backup_type_ar = {
                'full': 'كامل',
                'warehouse': 'مستودع'
            }.get(schedule.backup_type, schedule.backup_type)
            
            warehouse_name = schedule.warehouse.name if schedule.warehouse else "جميع المستودعات"
            
            print(f"📋 تفاصيل الجدولة:")
            print(f"   النوع: {schedule_type_ar}")
            print(f"   نوع النسخة: {backup_type_ar}")
            print(f"   المستودع: {warehouse_name}")
            print(f"   الحالة: {'نشط' if schedule.is_active else 'متوقف'}")
            
            # تأكيد الحذف
            confirm = input("\n❓ هل أنت متأكد من حذف هذه الجدولة؟ (y/N): ").strip().lower()
            
            if confirm in ['y', 'yes', 'نعم']:
                db.session.delete(schedule)
                db.session.commit()
                
                print("✅ تم حذف الجدولة بنجاح")
                return True
            else:
                print("❌ تم إلغاء عملية الحذف")
                return False
            
    except Exception as e:
        print(f"❌ خطأ في حذف الجدولة: {str(e)}")
        return False

def create_schedule():
    """إنشاء جدولة جديدة"""
    
    print("➕ إنشاء جدولة جديدة:")
    print("=" * 40)
    
    try:
        from app import create_app
        from utils import create_backup_schedule
        
        app = create_app()
        with app.app_context():
            # جمع البيانات من المستخدم
            print("📝 أدخل تفاصيل الجدولة:")
            
            # نوع الجدولة
            print("\n1. نوع الجدولة:")
            print("   1 - يومي (daily)")
            print("   2 - أسبوعي (weekly)")
            print("   3 - شهري (monthly)")
            
            schedule_type_choice = input("اختر (1-3): ").strip()
            schedule_types = {'1': 'daily', '2': 'weekly', '3': 'monthly'}
            schedule_type = schedule_types.get(schedule_type_choice, 'daily')
            
            # نوع النسخة
            print("\n2. نوع النسخة:")
            print("   1 - كامل (full)")
            print("   2 - مستودع محدد (warehouse)")
            
            backup_type_choice = input("اختر (1-2): ").strip()
            backup_types = {'1': 'full', '2': 'warehouse'}
            backup_type = backup_types.get(backup_type_choice, 'full')
            
            # الوقت
            print("\n3. وقت التشغيل:")
            hour = int(input("الساعة (0-23): ").strip() or "2")
            minute = int(input("الدقيقة (0-59): ").strip() or "0")
            
            # المستودع (إذا كان النوع مستودع محدد)
            warehouse_id = None
            if backup_type == 'warehouse':
                from models import Warehouse
                warehouses = Warehouse.query.all()
                
                print("\n4. اختر المستودع:")
                for i, warehouse in enumerate(warehouses, 1):
                    print(f"   {i} - {warehouse.name}")
                
                warehouse_choice = input(f"اختر (1-{len(warehouses)}): ").strip()
                try:
                    warehouse_index = int(warehouse_choice) - 1
                    warehouse_id = warehouses[warehouse_index].id
                except:
                    warehouse_id = warehouses[0].id if warehouses else None
            
            # إنشاء الجدولة
            schedule = create_backup_schedule(
                schedule_type=schedule_type,
                backup_type=backup_type,
                user_id=1,  # افتراضي
                warehouse_id=warehouse_id,
                hour=hour,
                minute=minute
            )
            
            if schedule:
                print(f"\n✅ تم إنشاء الجدولة بنجاح (ID: {schedule.id})")
                return True
            else:
                print("\n❌ فشل في إنشاء الجدولة")
                return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجدولة: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🚀 إدارة جدولة النسخ الاحتياطي")
    print("=" * 50)
    
    while True:
        print("\n📋 الخيارات المتاحة:")
        print("1. عرض جميع الجدولات")
        print("2. تفعيل/إيقاف جدولة")
        print("3. حذف جدولة")
        print("4. إنشاء جدولة جديدة")
        print("5. خروج")
        
        choice = input("\nاختر (1-5): ").strip()
        
        if choice == '1':
            schedules = show_schedules()
            
        elif choice == '2':
            schedules = show_schedules()
            if schedules:
                try:
                    schedule_id = int(input("\nأدخل ID الجدولة للتبديل: ").strip())
                    toggle_schedule(schedule_id)
                except ValueError:
                    print("❌ يرجى إدخال رقم صحيح")
            
        elif choice == '3':
            schedules = show_schedules()
            if schedules:
                try:
                    schedule_id = int(input("\nأدخل ID الجدولة للحذف: ").strip())
                    delete_schedule(schedule_id)
                except ValueError:
                    print("❌ يرجى إدخال رقم صحيح")
            
        elif choice == '4':
            create_schedule()
            
        elif choice == '5':
            print("👋 وداعاً!")
            break
            
        else:
            print("❌ خيار غير صحيح، يرجى المحاولة مرة أخرى")

if __name__ == "__main__":
    main()
