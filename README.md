# نظام عتاد
## Military Warehouse Management System

نظام شامل لإدارة مستودعات الذخيرة والمعدات العسكرية مع دعم كامل للغة العربية.

---

## 🚀 التثبيت السريع

### Windows:
```bash
# 1. تثبيت المتطلبات
install_requirements.bat

# 2. إعداد النظام
python setup_system.py

# 3. تشغيل النظام
start_system.bat
```

### Linux/Mac:
```bash
# 1. تثبيت المتطلبات
chmod +x install_requirements.sh
./install_requirements.sh

# 2. إعداد النظام
python setup_system.py

# 3. تشغيل النظام
chmod +x start_system.sh
./start_system.sh
```

---

## 📋 المتطلبات

- **Python 3.8+**
- **PostgreSQL 13+**
- **متصفح ويب حديث**

---

## 🔐 بيانات الدخول الافتراضية

- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

⚠️ **مهم**: غير كلمة المرور بعد أول تسجيل دخول

---

## 📚 الوثائق

للحصول على دليل التثبيت الشامل، راجع:
- `دليل_التثبيت_الشامل.md`

---

## 🎯 الميزات

- ✅ إدارة المستودعات والمخزون
- ✅ تتبع الأسلحة والمعدات
- ✅ إدارة الأفراد والصلاحيات
- ✅ كشوف الاستلامات والتسليم
- ✅ التقارير والإحصائيات
- ✅ دعم كامل للغة العربية
- ✅ واجهة مستخدم سهلة الاستخدام

---

## 👥 الأدوار والصلاحيات

- **مدير النظام**: صلاحية كاملة
- **مدير المستودعات**: إدارة المستودعات
- **مسؤول مخزون**: إدارة المخزون
- **مناوب السرية**: كشف الاستلامات والمواقع
- **مراقب**: عرض فقط

---

## 🛠️ الدعم الفني

في حالة وجود مشاكل:
1. راجع `دليل_التثبيت_الشامل.md`
2. تحقق من ملفات السجلات
3. تأكد من تشغيل PostgreSQL

---

## 📄 الترخيص

هذا النظام مطور للاستخدام العسكري الحكومي.
