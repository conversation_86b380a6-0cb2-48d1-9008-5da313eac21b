#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
تشخيص مشاكل ميزة ربط الأفراد بالمواقع
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from locations import init_locations_db, get_db_connection
from models import Personnel
from db import db

def debug_location_personnel():
    """تشخيص مشاكل ميزة ربط الأفراد بالمواقع"""
    
    with app.app_context():
        print("🔍 تشخيص مشاكل ميزة ربط الأفراد بالمواقع...")
        print("=" * 60)
        
        # تهيئة قاعدة البيانات
        print("📊 تهيئة قاعدة البيانات...")
        init_locations_db()
        print("✅ تم إنشاء الجداول بنجاح")
        
        # فحص الجداول
        print("\n🗄️ فحص الجداول...")
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # فحص جدول المواقع
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='locations'")
        if cursor.fetchone():
            print("✅ جدول locations موجود")
            cursor.execute("SELECT COUNT(*) FROM locations")
            count = cursor.fetchone()[0]
            print(f"   📊 عدد المواقع: {count}")
        else:
            print("❌ جدول locations غير موجود")
        
        # فحص جدول ربط الأفراد بالمواقع
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='location_personnel'")
        if cursor.fetchone():
            print("✅ جدول location_personnel موجود")
            cursor.execute("SELECT COUNT(*) FROM location_personnel")
            count = cursor.fetchone()[0]
            print(f"   📊 عدد الروابط: {count}")
            
            # عرض بنية الجدول
            cursor.execute("PRAGMA table_info(location_personnel)")
            columns = cursor.fetchall()
            print("   📋 أعمدة الجدول:")
            for col in columns:
                print(f"     - {col[1]} ({col[2]})")
        else:
            print("❌ جدول location_personnel غير موجود")
        
        # فحص جدول الأفراد
        print("\n👥 فحص الأفراد...")
        personnel_list = Personnel.query.all()
        print(f"✅ تم العثور على {len(personnel_list)} فرد")
        
        if personnel_list:
            print("   الأفراد المتاحين:")
            for person in personnel_list[:3]:  # عرض أول 3 أفراد
                print(f"     - {person.name}")
                print(f"       الرقم العسكري: {person.personnel_id}")
                print(f"       رقم الهوية: {person.phone}")
                print(f"       الحالة: {person.status}")
                print()
        
        # اختبار إضافة فرد لموقع
        print("🧪 اختبار إضافة فرد لموقع...")
        if personnel_list:
            # جلب أول موقع
            cursor.execute("SELECT id, name FROM locations LIMIT 1")
            location = cursor.fetchone()
            
            if location:
                location_id = location[0]
                location_name = location[1]
                person = personnel_list[0]
                
                print(f"   📍 الموقع: {location_name} (ID: {location_id})")
                print(f"   👤 الفرد: {person.name} (ID: {person.id})")
                
                # التحقق من عدم وجود الربط مسبقاً
                cursor.execute("""
                    SELECT id FROM location_personnel 
                    WHERE location_id = ? AND personnel_id = ? AND is_active = 1
                """, (location_id, person.id))
                
                existing = cursor.fetchone()
                if existing:
                    print("   ⚠️ الفرد مرتبط بالموقع مسبقاً")
                else:
                    # إضافة الربط
                    try:
                        cursor.execute("""
                            INSERT INTO location_personnel (location_id, personnel_id, shift_type, notes)
                            VALUES (?, ?, ?, ?)
                        """, (location_id, person.id, 'اختبار', 'ربط تجريبي'))
                        
                        conn.commit()
                        print("   ✅ تم ربط الفرد بالموقع بنجاح")
                        
                        # التحقق من الربط
                        cursor.execute("""
                            SELECT shift_type, notes FROM location_personnel 
                            WHERE location_id = ? AND personnel_id = ? AND is_active = 1
                        """, (location_id, person.id))
                        
                        result = cursor.fetchone()
                        if result:
                            print(f"   📋 نوع الوردية: {result[0]}")
                            print(f"   📝 الملاحظات: {result[1]}")
                        
                    except Exception as e:
                        print(f"   ❌ خطأ في ربط الفرد: {e}")
            else:
                print("   ❌ لا توجد مواقع للاختبار")
        else:
            print("   ❌ لا توجد أفراد للاختبار")
        
        conn.close()
        
        print(f"\n✅ انتهى التشخيص!")
        print(f"📝 إذا كانت جميع الفحوصات ناجحة، فالمشكلة قد تكون في:")
        print(f"   1. صلاحيات المستخدم")
        print(f"   2. JavaScript في المتصفح")
        print(f"   3. طلبات AJAX")

if __name__ == "__main__":
    debug_location_personnel()
