#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إضافة فرد جديد
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from locations import add_personnel_to_location
from models import Personnel

def test_new_personnel():
    """اختبار إضافة فرد جديد"""
    
    with app.app_context():
        print("🔧 اختبار إضافة فرد جديد...")
        print("=" * 50)
        
        # البحث عن فرد آخر
        personnel = Personnel.query.filter(Personnel.id != 41).first()
        
        if personnel:
            print(f"👤 الفرد المختار: {personnel.name}")
            print(f"📱 رقم الهوية: {personnel.phone}")
            print(f"🆔 الرقم العسكري: {personnel.personnel_id}")
            print(f"🆔 معرف الفرد: {personnel.id}")
            
            # اختبار إضافة الفرد لموقع جديد
            location_id = 49  # موقع آخر
            
            print(f"\n🔄 محاولة إضافة الفرد للموقع {location_id}...")
            success, message = add_personnel_to_location(location_id, personnel.id, 'صباحية', 'اختبار فرد جديد')
            
            if success:
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")
                
            # اختبار إضافة نفس الفرد مرة أخرى
            print(f"\n🔄 محاولة إضافة نفس الفرد مرة أخرى...")
            success2, message2 = add_personnel_to_location(location_id, personnel.id, 'مسائية', 'اختبار مكرر')
            
            if success2:
                print(f"✅ {message2}")
            else:
                print(f"❌ {message2}")
        else:
            print("❌ لم يتم العثور على أفراد آخرين للاختبار")

if __name__ == "__main__":
    test_new_personnel()
