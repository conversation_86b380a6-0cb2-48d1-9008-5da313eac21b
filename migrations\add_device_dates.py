from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
import os
from sqlalchemy import text

# Create a minimal Flask app
app = Flask(__name__)
app.config["SQLALCHEMY_DATABASE_URI"] = os.environ.get("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/military_warehouse")
app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False

# Initialize SQLAlchemy
db = SQLAlchemy(app)

# Initialize Flask-Migrate
migrate = Migrate(app, db)

def upgrade():
    """Add manufacturer, purchase_date, and warranty_end columns to devices table."""
    with app.app_context():
        sql = text("""
        ALTER TABLE devices
        ADD COLUMN IF NOT EXISTS manufacturer VARCHAR(100),
        ADD COLUMN IF NOT EXISTS purchase_date TIMESTAMP,
        ADD COLUMN IF NOT EXISTS warranty_end TIMESTAMP;
        """)
        with db.engine.connect() as conn:
            conn.execute(sql)
            conn.commit()
        print("Migration completed successfully!")

if __name__ == "__main__":
    upgrade()
