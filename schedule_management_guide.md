# دليل إدارة جدولة النسخ الاحتياطي
## Schedule Management Guide

### 🎯 **كيفية إيقاف أو حذف جدولة النسخ الاحتياطي**

---

## 📋 **الوصول إلى إدارة الجدولة:**

1. **افتح المتصفح** وانتقل إلى: `http://localhost:5000/reports/backup`
2. **انتقل إلى قسم "الجدولات الموجودة"** في أسفل الصفحة
3. **ستجد جدول يعرض جميع الجدولات** مع تفاصيلها

---

## ⏸️ **إيقاف الجدولة (مؤقت):**

### **الطريقة:**
1. **ابحث عن الجدولة** التي تريد إيقافها في الجدول
2. **في عمود "الإجراءات"** ستجد زر **⏸️ (إيقاف)**
3. **اضغط على زر الإيقاف** 
4. **ستتغير حالة الجدولة** من "نشط" إلى "متوقف"
5. **ستظهر رسالة تأكيد**: "تم إيقاف جدولة النسخ الاحتياطي بنجاح"

### **النتيجة:**
- ✅ **الجدولة ستتوقف عن العمل**
- ✅ **لن يتم إنشاء نسخ احتياطية تلقائية**
- ✅ **الجدولة ستبقى محفوظة** (يمكن تفعيلها لاحقاً)
- ✅ **يمكن إعادة تفعيلها** بالضغط على زر ▶️ (تشغيل)

---

## 🗑️ **حذف الجدولة (نهائي):**

### **الطريقة:**
1. **ابحث عن الجدولة** التي تريد حذفها في الجدول
2. **في عمود "الإجراءات"** ستجد زر **🗑️ (حذف)**
3. **اضغط على زر الحذف**
4. **ستظهر رسالة تأكيد**: "هل أنت متأكد من حذف هذه الجدولة؟ سيتم إيقاف النسخ التلقائية."
5. **اضغط "موافق"** للتأكيد
6. **ستظهر رسالة تأكيد**: "تم حذف جدولة النسخ الاحتياطي بنجاح"

### **النتيجة:**
- ✅ **الجدولة ستحذف نهائياً**
- ✅ **لن يتم إنشاء نسخ احتياطية تلقائية**
- ❌ **لا يمكن استرجاع الجدولة** (يجب إنشاؤها من جديد)

---

## 🔄 **إعادة تفعيل الجدولة:**

إذا كانت الجدولة **متوقفة** وتريد تفعيلها:

1. **ابحث عن الجدولة المتوقفة** (ستجد حالتها "متوقف")
2. **اضغط على زر ▶️ (تشغيل)**
3. **ستتغير حالة الجدولة** من "متوقف" إلى "نشط"
4. **سيتم حساب موعد التشغيل التالي** تلقائياً

---

## 📊 **فهم جدول الجدولات:**

### **الأعمدة:**
- **#**: رقم تسلسلي
- **نوع الجدولة**: يومي/أسبوعي/شهري
- **نوع النسخة**: كامل/مستودع محدد
- **الوقت**: وقت التشغيل (مثل: 3:30 ص)
- **المستودع**: المستودع المحدد أو "جميع المستودعات"
- **الحالة**: نشط (أخضر) أو متوقف (رمادي)
- **التشغيل التالي**: موعد النسخة التالية
- **الإجراءات**: أزرار التحكم

### **الأزرار:**
- **⏸️ (أصفر)**: إيقاف الجدولة النشطة
- **▶️ (أخضر)**: تفعيل الجدولة المتوقفة  
- **🗑️ (أحمر)**: حذف الجدولة نهائياً

---

## 🧪 **اختبار النظام:**

### **تشغيل الاختبار التلقائي:**
```bash
python test_schedule_management.py
```

### **الاختبار اليدوي:**
1. **أنشئ جدولة جديدة**
2. **جرب إيقافها** ولاحظ تغير الحالة
3. **جرب تفعيلها مرة أخرى**
4. **جرب حذفها نهائياً**

---

## ⚠️ **تحذيرات مهمة:**

### **عند الإيقاف:**
- ✅ **الجدولة محفوظة** ويمكن تفعيلها لاحقاً
- ✅ **النسخ السابقة محفوظة**
- ❌ **لن يتم إنشاء نسخ جديدة** حتى إعادة التفعيل

### **عند الحذف:**
- ❌ **الجدولة ستحذف نهائياً**
- ✅ **النسخ السابقة محفوظة**
- ❌ **يجب إنشاء جدولة جديدة** للعودة للنسخ التلقائي

---

## 🔍 **استكشاف الأخطاء:**

### **إذا لم تظهر الأزرار:**
1. تأكد من تحديث الصفحة
2. تحقق من صلاحيات المستخدم
3. تأكد من وجود جدولات في قاعدة البيانات

### **إذا لم تعمل الأزرار:**
1. تحقق من اتصال الإنترنت
2. راجع سجلات المتصفح (F12)
3. تأكد من تشغيل التطبيق

### **إذا ظهرت أخطاء:**
1. راجع ملف `app.log`
2. تحقق من حالة قاعدة البيانات
3. أعد تشغيل التطبيق

---

## 📞 **الدعم:**

إذا واجهت أي مشاكل:
1. **شغل الاختبار**: `python test_schedule_management.py`
2. **راجع السجلات**: `tail -f app.log`
3. **تحقق من قاعدة البيانات**: `SELECT * FROM backup_schedules;`

---

## 🎯 **الخلاصة:**

**الآن يمكنك بسهولة:**
- ✅ **إيقاف الجدولات** مؤقتاً عند عدم الحاجة
- ✅ **حذف الجدولات** نهائياً عند عدم الرغبة فيها
- ✅ **إعادة تفعيل الجدولات** عند الحاجة
- ✅ **مراقبة حالة جميع الجدولات** في مكان واحد

**نظام إدارة الجدولة جاهز للاستخدام! 🎉**
