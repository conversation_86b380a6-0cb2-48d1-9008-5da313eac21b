# تعليمات إعداد جدولة النسخ الاحتياطي
## Setup Instructions for Backup Scheduler

### 🔧 الطريقة الأولى: Windows Task Scheduler (الموصى بها)

#### 1. إعداد المهمة تلقائياً:
```cmd
# تشغيل كمدير (Run as Administrator)
schtasks /create /xml "backup_scheduler_task.xml" /tn "Military Warehouse Backup Scheduler"
```

#### 2. إعداد المهمة يدوياً:
1. افتح **Task Scheduler** (مجدول المهام)
2. اضغط على **Create Task** (إنشاء مهمة)
3. في تبويب **General**:
   - Name: `Military Warehouse Backup Scheduler`
   - Description: `مهمة تشغيل النسخ الاحتياطية المجدولة`
   - اختر **Run whether user is logged on or not**
   - اختر **Run with highest privileges**

4. في تبويب **Triggers**:
   - اضغط **New**
   - Begin the task: **On a schedule**
   - Settings: **Daily**
   - Repeat task every: **5 minutes**
   - for a duration of: **Indefinitely**

5. في تبويب **Actions**:
   - اضغط **New**
   - Action: **Start a program**
   - Program/script: `C:\Users\<USER>\Desktop\iMQS_\run_scheduled_backups.bat`
   - Start in: `C:\Users\<USER>\Desktop\iMQS_`

6. في تبويب **Settings**:
   - ✅ Allow task to be run on demand
   - ✅ Run task as soon as possible after a scheduled start is missed
   - ✅ If the task fails, restart every: **1 minute**
   - ✅ Attempt to restart up to: **3 times**

#### 3. التحقق من المهمة:
```cmd
# عرض المهمة
schtasks /query /tn "Military Warehouse Backup Scheduler"

# تشغيل المهمة يدوياً للاختبار
schtasks /run /tn "Military Warehouse Backup Scheduler"

# حذف المهمة (إذا لزم الأمر)
schtasks /delete /tn "Military Warehouse Backup Scheduler" /f
```

---

### 🐍 الطريقة الثانية: تشغيل الخدمة مباشرة

#### 1. تشغيل الخدمة في الخلفية:
```cmd
# الانتقال لمجلد المشروع
cd C:\Users\<USER>\Desktop\iMQS_

# تشغيل الخدمة كـ daemon
python scheduler_service.py --daemon --interval 300
```

#### 2. تشغيل فحص واحد للاختبار:
```cmd
python scheduler_service.py --once
```

---

### 🌐 الطريقة الثالثة: استخدام Flask endpoint

#### 1. تشغيل عبر المتصفح:
```
GET http://localhost:5000/reports/run-scheduled-backups
```

#### 2. تشغيل عبر curl:
```cmd
curl -X POST http://localhost:5000/reports/run-scheduled-backups
```

#### 3. فحص حالة الجدولة:
```
GET http://localhost:5000/reports/scheduler-status
```

---

### 📋 مراقبة النظام

#### 1. فحص سجلات النشاط:
- ملف `scheduler.log` - سجلات Windows Task Scheduler
- ملف `scheduler_service.log` - سجلات خدمة Python
- ملف `app.log` - سجلات التطبيق الرئيسي

#### 2. فحص قاعدة البيانات:
```sql
-- فحص الجدولات النشطة
SELECT * FROM backup_schedules WHERE is_active = true;

-- فحص النسخ الاحتياطية الأخيرة
SELECT * FROM backup_records ORDER BY timestamp DESC LIMIT 10;

-- فحص سجلات النشاط
SELECT * FROM activity_logs WHERE action LIKE '%نسخ%' ORDER BY timestamp DESC LIMIT 10;
```

---

### ⚠️ ملاحظات مهمة:

1. **تعديل المسارات**: تأكد من تعديل المسارات في الملفات حسب موقع مشروعك
2. **الصلاحيات**: تأكد من تشغيل Task Scheduler بصلاحيات المدير
3. **Python Path**: تأكد من أن Python موجود في PATH أو استخدم المسار الكامل
4. **الشبكة**: تأكد من أن التطبيق يعمل ويمكن الوصول إليه
5. **قاعدة البيانات**: تأكد من أن قاعدة البيانات متاحة ويمكن الكتابة عليها

---

### 🔍 استكشاف الأخطاء:

#### إذا لم تعمل المهمة:
1. تحقق من سجلات Task Scheduler
2. تحقق من ملف `scheduler.log`
3. جرب تشغيل `run_scheduled_backups.bat` يدوياً
4. تحقق من صلاحيات الملفات والمجلدات
5. تأكد من أن Python يعمل بشكل صحيح

#### إذا لم تظهر النسخ الاحتياطية:
1. تحقق من وجود جدولات نشطة في قاعدة البيانات
2. تحقق من أن أوقات الجدولة صحيحة
3. جرب تشغيل النسخ يدوياً من واجهة الويب
4. تحقق من مساحة القرص الصلب

---

### 📞 الدعم:
إذا واجهت أي مشاكل، تحقق من:
- سجلات النظام
- حالة قاعدة البيانات  
- صلاحيات الملفات
- إعدادات الشبكة
