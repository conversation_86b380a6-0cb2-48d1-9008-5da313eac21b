#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار API البحث عن الأفراد
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from locations import get_db_connection

def test_search_api():
    """اختبار API البحث عن الأفراد"""
    
    with app.app_context():
        print("🔍 اختبار API البحث عن الأفراد...")
        print("=" * 50)
        
        # البحث في قاعدة البيانات مباشرة
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # جلب جميع الأفراد
        cursor.execute("SELECT * FROM personnel LIMIT 5")
        all_personnel = cursor.fetchall()
        
        print("👥 عينة من الأفراد في قاعدة البيانات:")
        for person in all_personnel:
            print(f"  - {person['name']}")
            print(f"    الرقم العسكري: {person['personnel_id']}")
            print(f"    رقم الهوية: {person['phone']}")
            print(f"    ID: {person['id']}")
            print()
        
        # اختبار البحث بأرقام مختلفة
        search_terms = ["1063224012", "1063224016", "1063224014"]
        
        for search_term in search_terms:
            print(f"🔍 البحث عن: {search_term}")
            
            # البحث في جدول الأفراد
            cursor.execute("""
                SELECT * FROM personnel 
                WHERE phone = ? OR personnel_id = ?
            """, (search_term, search_term))
            
            results = cursor.fetchall()
            
            if results:
                print(f"  ✅ وجدت {len(results)} نتيجة:")
                for person in results:
                    print(f"    - {person['name']} (ID: {person['id']})")
                    print(f"      الرقم العسكري: {person['personnel_id']}")
                    print(f"      رقم الهوية: {person['phone']}")
                    
                    # التحقق من المواقع المرتبطة
                    cursor.execute("""
                        SELECT l.id, l.name FROM location_personnel lp
                        JOIN locations l ON lp.location_id = l.id
                        WHERE lp.personnel_id = ? AND lp.is_active = 1
                    """, (person['id'],))
                    
                    linked_locations = cursor.fetchall()
                    if linked_locations:
                        print(f"      📍 مرتبط بالمواقع:")
                        for loc in linked_locations:
                            print(f"        - {loc['name']} (ID: {loc['id']})")
                    else:
                        print(f"      📍 غير مرتبط بأي موقع")
            else:
                print(f"  ❌ لا توجد نتائج")
            print()
        
        conn.close()

if __name__ == "__main__":
    test_search_api()
