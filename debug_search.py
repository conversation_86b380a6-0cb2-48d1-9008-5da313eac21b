#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة البحث
"""

import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_search():
    """تشخيص مشكلة البحث"""
    
    try:
        from app import create_app
        from db import db
        from models import Personnel, User
        from flask import request
        
        app = create_app()
        
        with app.app_context():
            print("🔍 تشخيص مشكلة البحث...")
            print("=" * 50)
            
            # 1. فحص الأفراد الموجودين
            print("1️⃣ فحص الأفراد الموجودين:")
            personnel_list = Personnel.query.all()
            for person in personnel_list:
                print(f"   📱 {person.phone or person.personnel_id} → {person.name}")
            
            # 2. فحص مستخدم مناوب السرية
            print("\n2️⃣ فحص مستخدم مناوب السرية:")
            company_duty = User.query.filter_by(role_en='company_duty').first()
            if company_duty:
                print(f"   👤 اسم المستخدم: {company_duty.username}")
                print(f"   🔐 الدور: {company_duty.user_role}")
                print(f"   ✅ is_company_duty: {company_duty.is_company_duty}")
                print(f"   ✅ is_admin_role: {company_duty.is_admin_role}")
            else:
                print("   ❌ لا يوجد مستخدم مناوب السرية")
            
            # 3. محاكاة البحث
            print("\n3️⃣ محاكاة البحث:")
            test_id = '1063224011'
            print(f"   🔍 البحث عن: {test_id}")
            
            # محاكاة الكود في receipts.py
            national_id = test_id.strip()
            
            if not national_id:
                print("   ❌ رقم الهوية فارغ")
                return
            
            if len(national_id) < 10:
                print("   ❌ رقم الهوية غير مكتمل")
                return
            
            # البحث في قاعدة البيانات
            personnel = None
            if company_duty and (company_duty.is_admin_role or company_duty.is_company_duty):
                print("   ✅ المستخدم لديه صلاحية البحث")
                personnel = Personnel.query.filter_by(phone=national_id).first()
                if not personnel:
                    personnel = Personnel.query.filter_by(personnel_id=national_id).first()
            else:
                print("   ❌ المستخدم ليس لديه صلاحية البحث")
            
            if personnel:
                print(f"   ✅ تم العثور على: {personnel.name}")
                
                # تحديد رقم الهوية
                national_id_value = personnel.phone if personnel.phone and len(personnel.phone) == 10 else personnel.personnel_id
                
                response_data = {
                    'success': True, 
                    'personnel': {
                        'national_id': national_id_value,
                        'name': personnel.name,
                        'rank': personnel.rank,
                        'unit': personnel.warehouse.name if personnel.warehouse else 'غير محدد'
                    }
                }
                
                print(f"   📤 الاستجابة: {response_data}")
            else:
                print("   ❌ لم يتم العثور على الفرد")
            
            # 4. فحص الـ endpoint مباشرة
            print("\n4️⃣ فحص الـ endpoint مباشرة:")
            
            # استيراد الـ blueprint
            from receipts import search_personnel
            
            # محاكاة request
            with app.test_request_context(f'/receipts/personnel/search?national_id={test_id}'):
                # محاكاة تسجيل دخول المستخدم
                from flask_login import login_user
                if company_duty:
                    login_user(company_duty)
                    
                    # استدعاء الدالة
                    try:
                        response = search_personnel()
                        print(f"   📤 استجابة الـ endpoint: {response.get_json()}")
                    except Exception as e:
                        print(f"   ❌ خطأ في الـ endpoint: {str(e)}")
                else:
                    print("   ❌ لا يمكن محاكاة تسجيل الدخول")
            
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_search()
