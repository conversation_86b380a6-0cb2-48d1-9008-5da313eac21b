#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد النظام التلقائي
Automatic System Setup

هذا الملف يقوم بإعداد النظام تلقائياً بعد تثبيت قاعدة البيانات
"""

import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_system():
    """إعداد النظام تلقائياً"""
    
    print("🚀 بدء إعداد نظام عتاد...")
    print("=" * 60)
    
    try:
        # 1. إنشاء التطبيق وقاعدة البيانات
        print("\n📊 1. إنشاء قاعدة البيانات والجداول...")
        from app import create_app
        from db import db
        
        app = create_app()
        
        with app.app_context():
            # إنشاء جميع الجداول
            db.create_all()
            print("   ✅ تم إنشاء جميع الجداول بنجاح")
            
            # 2. إنشاء المستودعات الافتراضية
            print("\n🏢 2. إنشاء المستودعات الافتراضية...")
            from models import Warehouse
            
            default_warehouses = [
                {"name": "المستودع الأول", "location": "المنطقة الأولى", "description": "المستودع الرئيسي الأول"},
                {"name": "المستودع الثاني", "location": "المنطقة الثانية", "description": "المستودع الرئيسي الثاني"},
                {"name": "المخزون العام", "location": "المنطقة المركزية", "description": "مخزون عام للمواد المشتركة"}
            ]
            
            for warehouse_data in default_warehouses:
                existing = Warehouse.query.filter_by(name=warehouse_data["name"]).first()
                if not existing:
                    warehouse = Warehouse(
                        name=warehouse_data["name"],
                        location=warehouse_data["location"],
                        description=warehouse_data["description"]
                    )
                    db.session.add(warehouse)
                    print(f"   ✅ تم إنشاء {warehouse_data['name']}")
                else:
                    print(f"   ℹ️  {warehouse_data['name']} موجود بالفعل")
            
            # 3. إنشاء المستخدم الافتراضي
            print("\n👤 3. إنشاء المستخدم الافتراضي...")
            from models import User
            
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    full_name='مدير النظام',
                    role='admin',
                    user_role='مدير نظام',
                    role_en='admin',
                    is_admin=True
                )
                admin_user.set_password('admin123')
                
                # إضافة صلاحية على جميع المستودعات
                warehouses = Warehouse.query.all()
                admin_user.warehouses.extend(warehouses)
                
                db.session.add(admin_user)
                print("   ✅ تم إنشاء المستخدم الافتراضي")
                print("   📝 اسم المستخدم: admin")
                print("   🔑 كلمة المرور: admin123")
                print("   ⚠️  يُرجى تغيير كلمة المرور بعد أول تسجيل دخول")
            else:
                print("   ℹ️  المستخدم الافتراضي موجود بالفعل")
            
            # 4. إنشاء المواقع الافتراضية
            print("\n📍 4. إنشاء المواقع الافتراضية...")
            from models import Location
            
            default_locations = [
                {"name": "المقر الرئيسي", "description": "المقر الرئيسي للوحدة"},
                {"name": "المبنى الإداري", "description": "المبنى الإداري"},
                {"name": "ساحة التدريب", "description": "ساحة التدريب العسكري"},
                {"name": "المستودع المركزي", "description": "المستودع المركزي للمعدات"}
            ]
            
            for location_data in default_locations:
                existing = Location.query.filter_by(name=location_data["name"]).first()
                if not existing:
                    location = Location(
                        name=location_data["name"],
                        description=location_data["description"]
                    )
                    db.session.add(location)
                    print(f"   ✅ تم إنشاء موقع: {location_data['name']}")
                else:
                    print(f"   ℹ️  موقع {location_data['name']} موجود بالفعل")
            
            # 5. إنشاء الحالات الافتراضية
            print("\n🔄 5. إنشاء الحالات الافتراضية...")
            from models import Status
            
            default_statuses = [
                {"name": "جديد", "description": "عنصر جديد لم يستخدم"},
                {"name": "مستخدم", "description": "عنصر مستخدم بحالة جيدة"},
                {"name": "يحتاج صيانة", "description": "عنصر يحتاج إلى صيانة"},
                {"name": "خارج الخدمة", "description": "عنصر خارج الخدمة"},
                {"name": "مفقود", "description": "عنصر مفقود"}
            ]
            
            for status_data in default_statuses:
                existing = Status.query.filter_by(name=status_data["name"]).first()
                if not existing:
                    status = Status(
                        name=status_data["name"],
                        description=status_data["description"]
                    )
                    db.session.add(status)
                    print(f"   ✅ تم إنشاء حالة: {status_data['name']}")
                else:
                    print(f"   ℹ️  حالة {status_data['name']} موجودة بالفعل")
            
            # حفظ جميع التغييرات
            db.session.commit()
            print("\n💾 تم حفظ جميع البيانات بنجاح")
            
        print("\n" + "=" * 60)
        print("🎉 تم إعداد النظام بنجاح!")
        print("\n📋 ملخص الإعداد:")
        print("   ✅ قاعدة البيانات والجداول")
        print("   ✅ المستودعات الافتراضية (3)")
        print("   ✅ المستخدم الافتراضي (admin)")
        print("   ✅ المواقع الافتراضية (4)")
        print("   ✅ الحالات الافتراضية (5)")
        
        print("\n🚀 لتشغيل النظام:")
        print("   python app.py")
        
        print("\n🌐 رابط النظام:")
        print("   http://localhost:5000")
        
        print("\n🔐 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        
        print("\n⚠️  تذكير مهم:")
        print("   - غير كلمة مرور المدير بعد أول تسجيل دخول")
        print("   - أضف المستخدمين والبيانات حسب الحاجة")
        print("   - تأكد من عمل نسخ احتياطية دورية")
        
    except Exception as e:
        print(f"\n❌ خطأ في إعداد النظام: {str(e)}")
        import traceback
        traceback.print_exc()
        print("\n🔧 للمساعدة:")
        print("   1. تأكد من تشغيل PostgreSQL")
        print("   2. تحقق من إعدادات قاعدة البيانات في config.py")
        print("   3. تأكد من تثبيت جميع المكتبات المطلوبة")

if __name__ == "__main__":
    setup_system()
