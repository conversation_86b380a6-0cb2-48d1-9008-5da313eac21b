#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت شامل لربط جميع سندات الأسلحة الموجودة في المجلد بقاعدة البيانات
"""

import psycopg2
import os
import re

def sync_all_weapon_documents():
    """ربط جميع سندات الأسلحة بقاعدة البيانات"""
    
    try:
        # الاتصال بقاعدة البيانات
        conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
        cursor = conn.cursor()
        
        print("🔄 بدء عملية ربط جميع سندات الأسلحة...")
        
        # الحصول على جميع ملفات السندات
        documents_dir = 'static/uploads/weapon_documents'
        if not os.path.exists(documents_dir):
            print("❌ مجلد السندات غير موجود!")
            return
        
        files = os.listdir(documents_dir)
        print(f"📁 تم العثور على {len(files)} ملف في مجلد السندات")
        
        updated_count = 0
        not_found_count = 0
        already_linked_count = 0
        
        for file in files:
            # استخراج الرقم التسلسلي من اسم الملف
            # نمط: weapon_G-K4S1B-3480-0.091001634_hash.pdf
            match = re.search(r'weapon_(.+?)_[a-f0-9]+\.(pdf|jpg|jpeg|png|doc|docx)$', file)
            if match:
                serial_from_file = match.group(1)
                file_path = f"static/uploads/weapon_documents/{file}"
                
                # البحث عن السلاح في قاعدة البيانات
                cursor.execute('''
                    SELECT id, weapon_document 
                    FROM weapons 
                    WHERE serial_number = %s
                ''', (serial_from_file,))
                
                weapon = cursor.fetchone()
                if weapon:
                    weapon_id, current_doc = weapon
                    
                    # إذا لم يكن له سند مسجل، أضف المسار
                    if not current_doc:
                        cursor.execute('''
                            UPDATE weapons 
                            SET weapon_document = %s, updated_at = NOW()
                            WHERE id = %s
                        ''', (file_path, weapon_id))
                        
                        updated_count += 1
                        print(f"   ✅ تم ربط الملف {file} بالسلاح {serial_from_file}")
                    else:
                        already_linked_count += 1
                        print(f"   ℹ️  السلاح {serial_from_file} له سند مرتبط بالفعل")
                else:
                    not_found_count += 1
                    print(f"   ⚠️  لم يتم العثور على السلاح {serial_from_file} في قاعدة البيانات")
            else:
                print(f"   ❓ ملف بتنسيق غير متوقع: {file}")
        
        # حفظ التغييرات
        conn.commit()
        
        print(f"\n🎉 تمت العملية بنجاح!")
        print(f"   - تم ربط {updated_count} سلاح جديد بسندات")
        print(f"   - {already_linked_count} سلاح له سندات مرتبطة بالفعل")
        print(f"   - {not_found_count} ملف لأسلحة غير موجودة في قاعدة البيانات")
        
        # التحقق من النتائج النهائية
        cursor.execute('SELECT COUNT(*) FROM weapons WHERE weapon_document IS NOT NULL')
        total_with_docs = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM weapons')
        total_weapons = cursor.fetchone()[0]
        
        print(f"\n📊 الإحصائيات النهائية:")
        print(f"   - إجمالي الأسلحة: {total_weapons}")
        print(f"   - الأسلحة التي لها سندات: {total_with_docs}")
        print(f"   - النسبة المئوية: {(total_with_docs/total_weapons*100):.1f}%")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في العملية: {str(e)}")

def verify_specific_weapon():
    """التحقق من سلاح محدد"""
    
    try:
        conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
        cursor = conn.cursor()
        
        # التحقق من السلاح المحدد في الصورة
        serial = 'G-K4S1B-3480-0.091001634'
        
        cursor.execute('''
            SELECT id, serial_number, weapon_document 
            FROM weapons 
            WHERE serial_number = %s
        ''', (serial,))
        
        weapon = cursor.fetchone()
        if weapon:
            weapon_id, serial_num, doc_path = weapon
            print(f"🔍 السلاح المطلوب:")
            print(f"   - المعرف: {weapon_id}")
            print(f"   - الرقم التسلسلي: {serial_num}")
            print(f"   - مسار السند: {doc_path}")
            
            if doc_path:
                # التحقق من وجود الملف
                if os.path.exists(doc_path):
                    file_size = os.path.getsize(doc_path)
                    print(f"   ✅ الملف موجود: {doc_path}")
                    print(f"   📏 حجم الملف: {file_size:,} بايت")
                else:
                    print(f"   ❌ الملف غير موجود: {doc_path}")
            else:
                print(f"   ❌ لا يوجد سند مرتبط بهذا السلاح")
                
                # البحث عن ملف السند في المجلد
                documents_dir = 'static/uploads/weapon_documents'
                files = os.listdir(documents_dir)
                matching_files = [f for f in files if serial.replace('-', '').replace('.', '') in f.replace('-', '').replace('.', '')]
                
                if matching_files:
                    print(f"   🔍 تم العثور على ملفات مطابقة:")
                    for file in matching_files:
                        print(f"     - {file}")
                        
                    # ربط أول ملف مطابق
                    first_file = matching_files[0]
                    file_path = f"static/uploads/weapon_documents/{first_file}"
                    
                    cursor.execute('''
                        UPDATE weapons 
                        SET weapon_document = %s, updated_at = NOW()
                        WHERE id = %s
                    ''', (file_path, weapon_id))
                    
                    conn.commit()
                    print(f"   ✅ تم ربط الملف {first_file} بالسلاح")
        else:
            print(f"❌ لم يتم العثور على السلاح بالرقم التسلسلي: {serial}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {str(e)}")

if __name__ == '__main__':
    print("=" * 60)
    print("ربط جميع سندات الأسلحة بقاعدة البيانات")
    print("=" * 60)
    
    # التحقق من السلاح المحدد أولاً
    print("1️⃣ التحقق من السلاح المحدد...")
    verify_specific_weapon()
    
    print("\n" + "=" * 60)
    
    # ربط جميع السندات
    print("2️⃣ ربط جميع السندات...")
    sync_all_weapon_documents()
    
    print("\n" + "=" * 60)
    print("✅ تمت العملية بنجاح! يمكنك الآن تحديث الصفحة لرؤية السندات")
    print("=" * 60)
