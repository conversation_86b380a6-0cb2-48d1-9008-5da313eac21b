#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار API البحث مباشرة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import Personnel
from db import db

def test_api_direct():
    with app.app_context():
        print("=== اختبار API البحث مباشرة ===")
        
        # اختبار البحث في قاعدة البيانات
        search_terms = ['966565355339', '524299', '1063224016']
        
        for term in search_terms:
            print(f"\n🔍 البحث عن: {term}")
            
            # البحث في جميع الأفراد
            personnel = Personnel.query.filter(
                (Personnel.phone == term) | (Personnel.personnel_id == term)
            ).first()
            
            if personnel:
                print(f"✅ تم العثور على: {personnel.name}")
                print(f"   الرقم العسكري: {personnel.personnel_id}")
                print(f"   رقم الهوية: {personnel.phone}")
                print(f"   المستودع: {personnel.warehouse.name if personnel.warehouse else 'غير محدد'}")
                print(f"   معرف الفرد: {personnel.id}")
            else:
                print("❌ لم يتم العثور على نتائج")
        
        # اختبار محاكاة API response
        print(f"\n=== محاكاة API Response ===")
        test_term = '966565355339'
        personnel = Personnel.query.filter(
            (Personnel.phone == test_term) | (Personnel.personnel_id == test_term)
        ).first()
        
        if personnel:
            api_response = {
                'success': True,
                'personnel': {
                    'id': personnel.id,
                    'personnel_id': personnel.personnel_id,
                    'name': personnel.name,
                    'rank': personnel.rank,
                    'status': personnel.status,
                    'phone': personnel.phone,
                    'warehouse_name': personnel.warehouse.name if personnel.warehouse else 'غير محدد'
                }
            }
            print("API Response:")
            import json
            print(json.dumps(api_response, ensure_ascii=False, indent=2))
        else:
            api_response = {
                'success': False,
                'message': 'لم يتم العثور على أفراد بهذا الرقم'
            }
            print("API Response:")
            import json
            print(json.dumps(api_response, ensure_ascii=False, indent=2))

if __name__ == "__main__":
    test_api_direct()
