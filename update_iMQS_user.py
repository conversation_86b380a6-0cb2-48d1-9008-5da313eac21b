#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from db import db
from models import User

def update_iMQS_user():
    app = create_app()
    with app.app_context():
        try:
            # عرض جميع المستخدمين
            users = User.query.all()
            print(f"عدد المستخدمين: {len(users)}")

            for user in users:
                print(f"\nالمستخدم: {user.username}")
                print(f"الدور: {user.role}")
                print(f"النص: {user.user_role}")
                print(f"is_company_duty: {user.is_company_duty}")

                # إذا كان مناوب السرية، تحديثه
                if user.is_company_duty or user.user_role == 'مناوب السرية':
                    print("تحديث مناوب السرية...")
                    user.role = 'company_duty'
                    user.is_admin = False
                    print(f"محدث إلى: {user.role} - {user.user_role}")

            db.session.commit()
            print("\nتم التحديث بنجاح!")

        except Exception as e:
            print(f"خطأ: {e}")
            db.session.rollback()

if __name__ == "__main__":
    update_iMQS_user()
