#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص نموذج الأفراد
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from models import Personnel

def check_personnel_model():
    """فحص نموذج الأفراد"""
    
    with app.app_context():
        print("🔍 فحص نموذج الأفراد...")
        print("=" * 50)
        
        try:
            # محاولة جلب الأفراد
            personnel_count = Personnel.query.count()
            print(f"👥 عدد الأفراد في قاعدة البيانات: {personnel_count}")
            
            if personnel_count > 0:
                # جلب عينة من الأفراد
                sample_personnel = Personnel.query.limit(5).all()
                print("\n👤 عينة من الأفراد:")
                for person in sample_personnel:
                    print(f"  - {person.name}")
                    print(f"    الرقم العسكري: {person.personnel_id}")
                    print(f"    رقم الهوية: {person.phone}")
                    print(f"    الحالة: {person.status}")
                    print(f"    الرتبة: {person.rank}")
                    print()
            else:
                print("❌ لا يوجد أفراد في قاعدة البيانات")
                print("🔧 سأقوم بإنشاء بعض الأفراد التجريبيين...")
                
                # إنشاء أفراد تجريبيين
                test_personnel = [
                    {
                        'personnel_id': '1063224012',
                        'name': 'أحمد بن محمد العتيبي',
                        'phone': '1063224012',
                        'rank': 'عريف',
                        'status': 'نشط',
                        'warehouse_id': 1
                    },
                    {
                        'personnel_id': '1063224016',
                        'name': 'سعد بن عبدالله المطيري',
                        'phone': '1063224016',
                        'rank': 'جندي أول',
                        'status': 'نشط',
                        'warehouse_id': 1
                    },
                    {
                        'personnel_id': '1063224014',
                        'name': 'خالد بن سليمان الحربي',
                        'phone': '1063224014',
                        'rank': 'وكيل رقيب',
                        'status': 'نشط',
                        'warehouse_id': 1
                    }
                ]
                
                for person_data in test_personnel:
                    # التحقق من عدم وجود الفرد مسبقاً
                    existing = Personnel.query.filter_by(personnel_id=person_data['personnel_id']).first()
                    if not existing:
                        person = Personnel(**person_data)
                        db.session.add(person)
                        print(f"  ✅ تم إنشاء: {person_data['name']}")
                
                db.session.commit()
                print(f"\n✅ تم إنشاء {len(test_personnel)} أفراد تجريبيين")
                
                # إعادة فحص العدد
                personnel_count = Personnel.query.count()
                print(f"👥 العدد الجديد للأفراد: {personnel_count}")
                
        except Exception as e:
            print(f"❌ خطأ في فحص نموذج الأفراد: {e}")
            print("🔧 محاولة إنشاء الجداول...")
            
            try:
                db.create_all()
                print("✅ تم إنشاء الجداول بنجاح")
                
                # إعادة المحاولة
                check_personnel_model()
                
            except Exception as create_error:
                print(f"❌ خطأ في إنشاء الجداول: {create_error}")

if __name__ == "__main__":
    check_personnel_model()
