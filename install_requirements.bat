@echo off
chcp 65001 > nul
echo.
echo ========================================
echo    تثبيت متطلبات النظام
echo    Installing System Requirements
echo ========================================
echo.

echo 🔄 إنشاء البيئة الافتراضية...
python -m venv venv

echo.
echo 🔄 تفعيل البيئة الافتراضية...
call venv\Scripts\activate

echo.
echo 📦 تثبيت المكتبات المطلوبة...
pip install --upgrade pip
pip install -r requirements.txt

echo.
echo ✅ تم تثبيت جميع المتطلبات بنجاح!
echo.
echo 📋 الخطوات التالية:
echo    1. تأكد من تشغيل PostgreSQL
echo    2. أنشئ قاعدة بيانات باسم: military_warehouse
echo    3. حدث إعدادات قاعدة البيانات في config.py
echo    4. شغل: python setup_system.py
echo    5. شغل: start_system.bat
echo.
pause
