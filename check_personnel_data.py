#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بيانات الأفراد في قاعدة البيانات
"""

import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_personnel_data():
    """فحص بيانات الأفراد الموجودة"""
    
    try:
        from app import create_app
        from db import db
        from models import Personnel, Warehouse
        
        app = create_app()
        
        with app.app_context():
            print("🔍 فحص بيانات الأفراد...")
            print("=" * 50)
            
            # عدد الأفراد الإجمالي
            total_personnel = Personnel.query.count()
            print(f"📊 إجمالي عدد الأفراد: {total_personnel}")
            
            if total_personnel == 0:
                print("⚠️  لا توجد بيانات أفراد في النظام!")
                print("\n🔧 سأقوم بإضافة بيانات تجريبية...")
                add_sample_personnel()
            else:
                print("\n👥 الأفراد الموجودون:")
                personnel_list = Personnel.query.all()
                
                for i, person in enumerate(personnel_list, 1):
                    print(f"{i}. {person.name}")
                    print(f"   📱 رقم الهوية: {person.phone or person.personnel_id}")
                    print(f"   🎖️  الرتبة: {person.rank}")
                    print(f"   🏢 الوحدة: {person.warehouse.name if person.warehouse else 'غير محدد'}")
                    print()
                
                print("✅ يمكن للنظام الآن البحث عن هؤلاء الأفراد")
            
    except Exception as e:
        print(f"❌ خطأ في فحص البيانات: {str(e)}")
        import traceback
        traceback.print_exc()

def add_sample_personnel():
    """إضافة بيانات أفراد تجريبية"""
    
    try:
        from app import create_app
        from db import db
        from models import Personnel, Warehouse
        
        app = create_app()
        
        with app.app_context():
            # الحصول على المستودعات الموجودة
            warehouses = Warehouse.query.all()
            if not warehouses:
                print("❌ لا توجد مستودعات! يجب إنشاء المستودعات أولاً")
                return
            
            # بيانات أفراد تجريبية
            sample_personnel = [
                {
                    'personnel_id': 'M001',
                    'name': 'أحمد محمد علي',
                    'rank': 'رقيب',
                    'phone': '1234567890',  # رقم الهوية الوطنية
                    'status': 'نشط',
                    'warehouse_id': warehouses[0].id
                },
                {
                    'personnel_id': 'M002', 
                    'name': 'محمد أحمد سالم',
                    'rank': 'عريف',
                    'phone': '0987654321',
                    'status': 'نشط',
                    'warehouse_id': warehouses[1].id if len(warehouses) > 1 else warehouses[0].id
                },
                {
                    'personnel_id': 'M003',
                    'name': 'علي سعد محمد', 
                    'rank': 'جندي أول',
                    'phone': '1122334455',
                    'status': 'نشط',
                    'warehouse_id': warehouses[2].id if len(warehouses) > 2 else warehouses[0].id
                },
                {
                    'personnel_id': 'M004',
                    'name': 'سعد علي أحمد',
                    'rank': 'وكيل رقيب', 
                    'phone': '5544332211',
                    'status': 'نشط',
                    'warehouse_id': warehouses[0].id
                },
                {
                    'personnel_id': 'M005',
                    'name': 'خالد عبدالله محمد',
                    'rank': 'رقيب أول',
                    'phone': '1111222233',
                    'status': 'نشط',
                    'warehouse_id': warehouses[1].id if len(warehouses) > 1 else warehouses[0].id
                }
            ]
            
            print("📝 إضافة الأفراد التجريبيين...")
            
            for person_data in sample_personnel:
                # التحقق من عدم وجود الفرد مسبقاً
                existing = Personnel.query.filter_by(personnel_id=person_data['personnel_id']).first()
                if not existing:
                    person = Personnel(
                        personnel_id=person_data['personnel_id'],
                        name=person_data['name'],
                        rank=person_data['rank'],
                        phone=person_data['phone'],
                        status=person_data['status'],
                        warehouse_id=person_data['warehouse_id']
                    )
                    db.session.add(person)
                    print(f"   ✅ تم إضافة: {person_data['name']} (هوية: {person_data['phone']})")
                else:
                    print(f"   ℹ️  موجود مسبقاً: {person_data['name']}")
            
            db.session.commit()
            print("\n🎉 تم إضافة جميع الأفراد التجريبيين بنجاح!")
            
            print("\n📋 يمكنك الآن اختبار البحث بالأرقام التالية:")
            for person_data in sample_personnel:
                print(f"   🔍 {person_data['phone']} → {person_data['name']}")
            
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_personnel_data()
