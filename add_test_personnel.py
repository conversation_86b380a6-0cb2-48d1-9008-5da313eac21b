#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة أفراد تجريبيين للاختبار
"""

import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def add_test_personnel():
    """إضافة أفراد تجريبيين بأرقام هوية سهلة"""
    
    try:
        from app import create_app
        from db import db
        from models import Personnel, Warehouse
        
        app = create_app()
        
        with app.app_context():
            print("📝 إضافة أفراد تجريبيين...")
            print("=" * 50)
            
            # الحصول على المستودع الأول
            warehouse = Warehouse.query.first()
            if not warehouse:
                print("❌ لا توجد مستودعات!")
                return
            
            # أفراد تجريبيون بأرقام هوية سهلة
            test_personnel = [
                {
                    'personnel_id': 'T001',
                    'name': 'أحمد محمد علي',
                    'rank': 'رقيب',
                    'phone': '1234567890',
                    'status': 'نشط'
                },
                {
                    'personnel_id': 'T002',
                    'name': 'محمد أحمد سالم',
                    'rank': 'عريف',
                    'phone': '0987654321',
                    'status': 'نشط'
                },
                {
                    'personnel_id': 'T003',
                    'name': 'علي سعد محمد',
                    'rank': 'جندي أول',
                    'phone': '1111111111',
                    'status': 'نشط'
                },
                {
                    'personnel_id': 'T004',
                    'name': 'سعد علي أحمد',
                    'rank': 'وكيل رقيب',
                    'phone': '2222222222',
                    'status': 'نشط'
                },
                {
                    'personnel_id': 'T005',
                    'name': 'خالد عبدالله محمد',
                    'rank': 'رقيب أول',
                    'phone': '3333333333',
                    'status': 'نشط'
                }
            ]
            
            for person_data in test_personnel:
                # التحقق من عدم وجود الفرد
                existing = Personnel.query.filter_by(phone=person_data['phone']).first()
                if not existing:
                    person = Personnel(
                        personnel_id=person_data['personnel_id'],
                        name=person_data['name'],
                        rank=person_data['rank'],
                        phone=person_data['phone'],
                        status=person_data['status'],
                        warehouse_id=warehouse.id
                    )
                    db.session.add(person)
                    print(f"   ✅ تم إضافة: {person_data['name']} (هوية: {person_data['phone']})")
                else:
                    print(f"   ℹ️  موجود مسبقاً: {person_data['name']}")
            
            db.session.commit()
            
            print("\n🎉 تم إضافة الأفراد التجريبيين!")
            print("\n📋 أرقام الهوية للاختبار:")
            
            all_personnel = Personnel.query.all()
            for person in all_personnel:
                national_id = person.phone or person.personnel_id
                print(f"   🔍 {national_id} → {person.name}")
            
            print(f"\n📊 إجمالي عدد الأفراد: {len(all_personnel)}")
            
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    add_test_personnel()
