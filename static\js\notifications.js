/**
 * نظام إدارة الإشعارات المحسنة - تصميم احترافي
 * @version 2.0
 */
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة حاوية الإشعارات إذا لم تكن موجودة
    let notificationsContainer = document.getElementById('notifications-container');
    if (!notificationsContainer) {
        notificationsContainer = document.createElement('div');
        notificationsContainer.id = 'notifications-container';
        // نستخدم CSS الخارجي بدلاً من الأنماط المضمنة
        document.body.appendChild(notificationsContainer);
    }

    // إضافة متغير عام لتتبع عدد الإشعارات النشطة
    window.activeNotifications = window.activeNotifications || 0;

    // الحد الأقصى للإشعارات المعروضة في وقت واحد
    const MAX_NOTIFICATIONS = 3;

    // قائمة انتظار الإشعارات
    window.notificationQueue = window.notificationQueue || [];

    // تخزين الإشعارات التي تم عرضها لتجنب التكرار
    window.processedAlerts = window.processedAlerts || new Set();

    // قائمة لتخزين الإشعارات المعالجة مؤخرًا (للتحقق من التكرار)
    window.recentlyProcessedMessages = window.recentlyProcessedMessages || [];

    // الحد الأقصى لعدد الإشعارات المخزنة في قائمة الإشعارات المعالجة مؤخرًا
    const MAX_RECENT_MESSAGES = 20;

    // تحويل الإشعارات الموجودة إلى النمط الجديد
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => {
        // إنشاء إشعار جديد بنفس المحتوى
        const message = alert.innerHTML.replace(/<button.*?button>/g, '').trim();

        // تجاهل الإشعارات التحذيرية المحددة التي لا يجب عرضها تلقائياً
        const ignoredMessages = [
            'تحذير: هذا الإجراء لا يمكن التراجع عنه',
            'هذا الإجراء لا يمكن التراجع عنه'
        ];

        const shouldIgnore = ignoredMessages.some(ignoredMsg =>
            message.includes(ignoredMsg) || alert.textContent.includes(ignoredMsg)
        );

        if (shouldIgnore) {
            // إزالة الإشعار بدون عرضه
            alert.remove();
            return;
        }

        // تحديد نوع الإشعار
        let type = 'info';
        if (alert.classList.contains('alert-success')) type = 'success';
        if (alert.classList.contains('alert-danger')) type = 'danger';
        if (alert.classList.contains('alert-warning')) type = 'warning';
        if (alert.classList.contains('alert-info')) type = 'info';

        // استخدام نظام الإشعارات المحسن الذي يتحقق من التكرار تلقائيًا
        showNotification(message, type);

        // إزالة الإشعار القديم
        alert.remove();
    });

    // إضافة معالج للإشعارات المستقبلية
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.addedNodes.length) {
                mutation.addedNodes.forEach(node => {
                    if (node.classList && node.classList.contains('alert')) {
                        // إنشاء إشعار جديد بنفس المحتوى
                        const message = node.innerHTML.replace(/<button.*?button>/g, '').trim();

                        // تجاهل الإشعارات التحذيرية المحددة التي لا يجب عرضها تلقائياً
                        const ignoredMessages = [
                            'تحذير: هذا الإجراء لا يمكن التراجع عنه',
                            'هذا الإجراء لا يمكن التراجع عنه'
                        ];

                        const shouldIgnore = ignoredMessages.some(ignoredMsg =>
                            message.includes(ignoredMsg) || node.textContent.includes(ignoredMsg)
                        );

                        if (shouldIgnore) {
                            // إزالة الإشعار بدون عرضه
                            node.remove();
                            return;
                        }

                        // تحديد نوع الإشعار
                        let type = 'info';
                        if (node.classList.contains('alert-success')) type = 'success';
                        if (node.classList.contains('alert-danger')) type = 'danger';
                        if (node.classList.contains('alert-warning')) type = 'warning';
                        if (node.classList.contains('alert-info')) type = 'info';

                        // استخدام نظام الإشعارات المحسن الذي يتحقق من التكرار تلقائيًا
                        showNotification(message, type);

                        // إزالة الإشعار القديم
                        node.remove();
                    }
                });
            }
        });
    });

    // مراقبة التغييرات في حاوية الإشعارات الأصلية
    const alertsContainer = document.getElementById('alerts-container');
    if (alertsContainer) {
        observer.observe(alertsContainer, { childList: true });
    }

    // إضافة دعم للإشعارات عبر الواجهة البرمجية
    window.notifications = {
        success: (message, duration) => showNotification(message, 'success', duration),
        error: (message, duration) => showNotification(message, 'danger', duration),
        warning: (message, duration) => showNotification(message, 'warning', duration),
        info: (message, duration) => showNotification(message, 'info', duration)
    };

    // إضافة دعم للإشعارات عبر الأحداث
    document.addEventListener('notification', function(e) {
        const { message, type, duration } = e.detail;
        showNotification(message, type, duration);
    });

    // معالجة قائمة انتظار الإشعارات
    function processNotificationQueue() {
        if (window.notificationQueue.length > 0 && window.activeNotifications < MAX_NOTIFICATIONS) {
            const nextNotification = window.notificationQueue.shift();
            displayNotification(nextNotification.message, nextNotification.type, nextNotification.duration);
        }
    }

    // تعريف دالة عرض الإشعار الفعلية
    window.displayNotification = displayNotification;
});

/**
 * عرض إشعار جديد - يضيف الإشعار إلى قائمة الانتظار أو يعرضه مباشرة
 * @param {string} message - نص الإشعار
 * @param {string} type - نوع الإشعار (success, danger, warning, info)
 * @param {number} duration - مدة ظهور الإشعار بالمللي ثانية (الافتراضي: 5000)
 */
function showNotification(message, type = 'info', duration = 5000) {
    // التحقق من وجود إشعار مطابق بالفعل
    const isDuplicate = checkForDuplicateNotification(message, type);
    if (isDuplicate) {
        console.log('تم تجاهل إشعار مكرر:', message);
        return null;
    }

    // إضافة الرسالة إلى قائمة الإشعارات المعالجة مؤخرًا
    addToRecentlyProcessedMessages(message, type);

    // إذا كان عدد الإشعارات النشطة أقل من الحد الأقصى، عرض الإشعار مباشرة
    if (window.activeNotifications < 3) {
        return displayNotification(message, type, duration);
    } else {
        // التحقق من وجود رسالة مطابقة في قائمة الانتظار
        const queueHasDuplicate = window.notificationQueue.some(item => {
            const simplifiedQueueMessage = simplifyMessageForComparison(item.message);
            const simplifiedNewMessage = simplifyMessageForComparison(message);
            return (item.type === type &&
                   (simplifiedQueueMessage === simplifiedNewMessage ||
                    areMessagesVerySimilar(simplifiedQueueMessage, simplifiedNewMessage)));
        });

        // إضافة الإشعار إلى قائمة الانتظار فقط إذا لم يكن مكررًا
        if (!queueHasDuplicate) {
            window.notificationQueue.push({ message, type, duration });
        }
        return null;
    }
}

/**
 * إضافة رسالة إلى قائمة الإشعارات المعالجة مؤخرًا
 * @param {string} message - نص الإشعار
 * @param {string} type - نوع الإشعار
 */
function addToRecentlyProcessedMessages(message, type) {
    // التأكد من وجود المصفوفة
    if (!window.recentlyProcessedMessages) {
        window.recentlyProcessedMessages = [];
    }

    // إضافة الرسالة إلى بداية المصفوفة
    window.recentlyProcessedMessages.unshift({
        message: message,
        type: type,
        timestamp: Date.now()
    });

    // الحفاظ على حجم المصفوفة (الاحتفاظ بآخر 20 رسالة فقط)
    if (window.recentlyProcessedMessages.length > 20) {
        window.recentlyProcessedMessages.pop();
    }

    // حذف الرسائل القديمة (أكثر من 30 ثانية)
    const now = Date.now();
    window.recentlyProcessedMessages = window.recentlyProcessedMessages.filter(item => {
        return (now - item.timestamp) < 30000; // 30 ثانية
    });
}

/**
 * التحقق من وجود إشعار مطابق بالفعل في الإشعارات النشطة
 * @param {string} message - نص الإشعار
 * @param {string} type - نوع الإشعار
 * @returns {boolean} - يعيد true إذا كان هناك إشعار مطابق
 */
function checkForDuplicateNotification(message, type) {
    const container = document.getElementById('notifications-container');
    if (!container) return false;

    // تنظيف النص الجديد من العلامات HTML
    const cleanNewMessage = message.replace(/<[^>]*>/g, '').trim();

    // إنشاء نسخة مبسطة من الرسالة للمقارنة (إزالة المسافات الزائدة والأحرف الخاصة)
    const simplifiedNewMessage = simplifyMessageForComparison(cleanNewMessage);

    // البحث عن جميع الإشعارات النشطة
    const activeNotifications = container.querySelectorAll('.notification');

    // التحقق من كل إشعار نشط
    for (let i = 0; i < activeNotifications.length; i++) {
        const notification = activeNotifications[i];

        // التحقق من نوع الإشعار (يمكن تجاوز هذا الشرط للتحقق من جميع الإشعارات بغض النظر عن النوع)
        // if (!notification.classList.contains(`notification-${type}`)) {
        //     continue;
        // }

        // الحصول على نص الإشعار
        const notificationMessage = notification.querySelector('.notification-message');
        if (!notificationMessage) continue;

        // تنظيف النص من العلامات HTML
        const cleanMessage = notificationMessage.textContent.trim();

        // تبسيط الرسالة الموجودة للمقارنة
        const simplifiedExistingMessage = simplifyMessageForComparison(cleanMessage);

        // مقارنة النصوص المبسطة
        if (simplifiedExistingMessage === simplifiedNewMessage) {
            console.log('تم العثور على إشعار مطابق بالضبط');
            return true;
        }

        // التحقق من التشابه الكبير بين الرسائل
        if (areMessagesVerySimilar(simplifiedExistingMessage, simplifiedNewMessage)) {
            console.log('تم العثور على إشعار مشابه جدًا');
            return true;
        }
    }

    // التحقق من قائمة الإشعارات المعالجة مؤخرًا
    if (window.recentlyProcessedMessages) {
        for (let i = 0; i < window.recentlyProcessedMessages.length; i++) {
            const recentMessage = window.recentlyProcessedMessages[i];
            if (recentMessage.type === type &&
                simplifyMessageForComparison(recentMessage.message) === simplifiedNewMessage) {
                console.log('تم العثور على إشعار في قائمة الإشعارات المعالجة مؤخرًا');
                return true;
            }
        }
    }

    return false;
}

/**
 * تبسيط الرسالة للمقارنة
 * @param {string} message - الرسالة الأصلية
 * @returns {string} - الرسالة المبسطة
 */
function simplifyMessageForComparison(message) {
    if (!message) return '';

    // إزالة جميع المسافات الزائدة
    let simplified = message.replace(/\s+/g, ' ').trim();

    // إزالة علامات الترقيم
    simplified = simplified.replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '');

    // تحويل إلى أحرف صغيرة (للمقارنة غير الحساسة لحالة الأحرف)
    simplified = simplified.toLowerCase();

    return simplified;
}

/**
 * التحقق من التشابه الكبير بين رسالتين
 * @param {string} message1 - الرسالة الأولى
 * @param {string} message2 - الرسالة الثانية
 * @returns {boolean} - يعيد true إذا كانت الرسالتان متشابهتين جدًا
 */
function areMessagesVerySimilar(message1, message2) {
    // إذا كانت إحدى الرسالتين فارغة
    if (!message1 || !message2) return false;

    // إذا كانت إحدى الرسالتين تحتوي على الأخرى
    if (message1.includes(message2) || message2.includes(message1)) {
        return true;
    }

    // حساب نسبة التشابه بين الرسالتين
    const similarity = calculateSimilarity(message1, message2);

    // إذا كانت نسبة التشابه أكبر من 80%
    return similarity > 0.8;
}

/**
 * حساب نسبة التشابه بين نصين
 * @param {string} s1 - النص الأول
 * @param {string} s2 - النص الثاني
 * @returns {number} - نسبة التشابه (0-1)
 */
function calculateSimilarity(s1, s2) {
    // إذا كان النصان متطابقين
    if (s1 === s2) return 1.0;

    // إذا كان أحد النصين فارغًا
    if (s1.length === 0 || s2.length === 0) return 0.0;

    // حساب عدد الأحرف المشتركة
    let matches = 0;
    const maxLength = Math.max(s1.length, s2.length);

    // مقارنة كل حرف
    for (let i = 0; i < s1.length; i++) {
        if (s2.includes(s1[i])) {
            matches++;
        }
    }

    // حساب نسبة التشابه
    return matches / maxLength;
}

/**
 * عرض إشعار جديد - الدالة الفعلية لعرض الإشعار
 * @param {string} message - نص الإشعار
 * @param {string} type - نوع الإشعار (success, danger, warning, info)
 * @param {number} duration - مدة ظهور الإشعار بالمللي ثانية (الافتراضي: 5000)
 */
function displayNotification(message, type = 'info', duration = 5000) {
    // زيادة عدد الإشعارات النشطة
    window.activeNotifications++;

    // تحديد أيقونة الإشعار حسب النوع
    let icon = '';
    let title = '';
    let defaultTitle = '';

    switch (type) {
        case 'success':
            icon = '<i class="fas fa-check-circle"></i>';
            defaultTitle = 'تم بنجاح';
            break;
        case 'danger':
            icon = '<i class="fas fa-exclamation-circle"></i>';
            defaultTitle = 'خطأ';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle"></i>';
            defaultTitle = 'تنبيه';
            break;
        case 'info':
        default:
            icon = '<i class="fas fa-info-circle"></i>';
            defaultTitle = 'معلومات';
            break;
    }

    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.setAttribute('role', 'alert');
    notification.setAttribute('aria-live', 'assertive');
    notification.setAttribute('data-notification-id', Date.now() + Math.random().toString(36).substr(2, 5));

    // تحليل الرسالة للتحقق مما إذا كانت تحتوي على عنوان
    let content = message;
    let hasCustomTitle = false;

    // إذا كانت الرسالة تحتوي على نمط "العنوان: المحتوى"
    if (message.includes(':') && message.indexOf(':') < 30) {
        const parts = message.split(':');
        title = parts[0].trim();
        content = parts.slice(1).join(':').trim();
        hasCustomTitle = true;
    }

    // تجنب تكرار العنوان في المحتوى
    // إذا كان المحتوى يبدأ بنفس العنوان الافتراضي، لا نعرض العنوان
    if (!hasCustomTitle && (
        content.startsWith(defaultTitle) ||
        content.toLowerCase().startsWith(defaultTitle.toLowerCase())
    )) {
        title = ''; // لا نعرض العنوان لتجنب التكرار
    } else if (!hasCustomTitle) {
        title = defaultTitle; // استخدام العنوان الافتراضي إذا لم يكن هناك عنوان مخصص
    }

    // إنشاء محتوى الإشعار
    notification.innerHTML = `
        <div class="notification-content">
            <div class="notification-icon">${icon}</div>
            <div class="notification-message">
                ${title ? `<div class="notification-title">${title}</div>` : ''}
                <div>${content}</div>
            </div>
        </div>
        <button type="button" class="notification-close" aria-label="إغلاق">
            <i class="fas fa-times"></i>
        </button>
    `;

    // إضافة الإشعار إلى الحاوية
    const container = document.getElementById('notifications-container');
    container.appendChild(notification);

    // إضافة معالج لزر الإغلاق
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        closeNotification(notification);
    });

    // إضافة معالج للنقر على الإشعار (إغلاق عند النقر المزدوج)
    notification.addEventListener('dblclick', () => {
        closeNotification(notification);
    });

    // إضافة معالج للتفاعل مع الإشعار (إيقاف المؤقت عند التحويم)
    let timeoutId = null;
    if (duration > 0) {
        timeoutId = setTimeout(() => {
            closeNotification(notification);
        }, duration);

        // إيقاف المؤقت عند التحويم
        notification.addEventListener('mouseenter', () => {
            clearTimeout(timeoutId);
        });

        // إعادة تشغيل المؤقت عند مغادرة التحويم
        notification.addEventListener('mouseleave', () => {
            timeoutId = setTimeout(() => {
                closeNotification(notification);
            }, duration / 2); // نصف المدة الأصلية عند مغادرة التحويم
        });
    }

    return notification;
}

/**
 * إغلاق إشعار
 * @param {HTMLElement} notification - عنصر الإشعار
 */
function closeNotification(notification) {
    // تجنب إغلاق الإشعار مرتين
    if (notification.classList.contains('closing')) {
        return;
    }

    notification.classList.add('closing');
    setTimeout(() => {
        notification.remove();

        // تقليل عدد الإشعارات النشطة
        window.activeNotifications--;

        // معالجة قائمة الانتظار
        if (window.notificationQueue && window.notificationQueue.length > 0) {
            const nextNotification = window.notificationQueue.shift();
            displayNotification(nextNotification.message, nextNotification.type, nextNotification.duration);
        }
    }, 400); // مدة الرسوم المتحركة
}
