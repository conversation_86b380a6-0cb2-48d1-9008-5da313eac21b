#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار ميزة ربط الأفراد بالمواقع
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from locations import init_locations_db, get_all_locations, get_location_personnel
from models import Personnel
from db import db

def test_location_personnel_feature():
    """اختبار ميزة ربط الأفراد بالمواقع"""
    
    with app.app_context():
        print("🔍 اختبار ميزة ربط الأفراد بالمواقع...")
        print("=" * 50)
        
        # تهيئة قاعدة البيانات
        print("📊 تهيئة قاعدة البيانات...")
        init_locations_db()
        print("✅ تم إنشاء الجداول بنجاح")
        
        # جلب المواقع
        print("\n📍 جلب المواقع...")
        locations = get_all_locations()
        print(f"✅ تم العثور على {len(locations)} موقع")
        
        for location in locations:
            print(f"\n🏢 الموقع: {location['name']}")
            print(f"   📊 عدد العهد: {len(location.get('equipment', []))}")
            print(f"   👥 عدد الأفراد: {location.get('personnel_count', 0)}")
            
            # جلب الأفراد المرتبطين بالموقع
            personnel = get_location_personnel(location['id'])
            if personnel:
                print(f"   الأفراد المرتبطين:")
                for person in personnel:
                    print(f"     - {person['name']} ({person['personnel_id']})")
            else:
                print(f"   لا يوجد أفراد مرتبطين بهذا الموقع")
        
        # جلب الأفراد المتاحين
        print(f"\n👥 الأفراد المتاحين في النظام:")
        personnel_list = Personnel.query.all()
        print(f"✅ تم العثور على {len(personnel_list)} فرد")
        
        for person in personnel_list[:5]:  # عرض أول 5 أفراد فقط
            print(f"   - {person.name} (الرقم العسكري: {person.personnel_id})")
            if person.phone:
                print(f"     رقم الهوية: {person.phone}")
        
        if len(personnel_list) > 5:
            print(f"   ... و {len(personnel_list) - 5} أفراد آخرين")
        
        print(f"\n✅ اختبار الميزة مكتمل!")
        print(f"📝 يمكنك الآن:")
        print(f"   1. الذهاب إلى صفحة إدارة المواقع")
        print(f"   2. اختيار موقع وعرض تفاصيله")
        print(f"   3. إضافة أفراد للموقع باستخدام رقم الهوية أو الرقم العسكري")
        print(f"   4. عرض قائمة الأفراد المرتبطين بالموقع")

if __name__ == "__main__":
    test_location_personnel_feature()
