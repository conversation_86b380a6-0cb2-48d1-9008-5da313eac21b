#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة صلاحية مناوب السرية إلى النظام
Add Company Duty role to the system

هذا الملف يقوم بإضافة صلاحية "مناوب السرية" إلى النظام
ويحدث قاعدة البيانات لتشمل هذه الصلاحية الجديدة
"""

import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from db import db
from models import User, UserRoleEnum

def add_company_duty_role():
    """إضافة صلاحية مناوب السرية إلى النظام"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 بدء إضافة صلاحية مناوب السرية...")
            
            # التحقق من وجود المستخدمين الحاليين
            users = User.query.all()
            print(f"📊 عدد المستخدمين الحاليين: {len(users)}")
            
            # عرض الصلاحيات الحالية
            print("\n📋 الصلاحيات الحالية في النظام:")
            for role in UserRoleEnum:
                print(f"   - {role.value}")
            
            # التحقق من وجود مستخدمين بصلاحية مناوب السرية
            from sqlalchemy import or_
            company_duty_users = User.query.filter(
                or_(User.role == 'company_duty', User.user_role == 'مناوب السرية')
            ).all()
            
            if company_duty_users:
                print(f"\n✅ يوجد بالفعل {len(company_duty_users)} مستخدم بصلاحية مناوب السرية:")
                for user in company_duty_users:
                    print(f"   - {user.username} ({user.user_role})")
            else:
                print("\n📝 لا يوجد مستخدمين بصلاحية مناوب السرية حالياً")
                
                # سؤال المستخدم إذا كان يريد إنشاء مستخدم جديد
                create_user = input("\n❓ هل تريد إنشاء مستخدم جديد بصلاحية مناوب السرية؟ (y/n): ").lower().strip()
                
                if create_user == 'y':
                    print("\n📝 إنشاء مستخدم جديد بصلاحية مناوب السرية:")
                    
                    username = input("اسم المستخدم: ").strip()
                    if not username:
                        print("❌ اسم المستخدم مطلوب")
                        return
                    
                    # التحقق من عدم وجود المستخدم
                    existing_user = User.query.filter_by(username=username).first()
                    if existing_user:
                        print(f"❌ المستخدم {username} موجود بالفعل")
                        return
                    
                    password = input("كلمة المرور: ").strip()
                    if not password:
                        print("❌ كلمة المرور مطلوبة")
                        return
                    
                    full_name = input("الاسم الكامل (اختياري): ").strip()
                    email = input("البريد الإلكتروني (اختياري): ").strip()
                    
                    # إنشاء المستخدم الجديد
                    new_user = User(
                        username=username,
                        email=email if email else None,
                        full_name=full_name if full_name else username,
                        role='company_duty',
                        user_role='مناوب السرية'
                    )
                    new_user.set_password(password)
                    
                    # مناوب السرية لا يحتاج إلى مستودعات (يعمل على كشف الاستلامات وإدارة المواقع فقط)
                    
                    db.session.add(new_user)
                    db.session.commit()
                    
                    print(f"✅ تم إنشاء المستخدم {username} بصلاحية مناوب السرية بنجاح")
                    print(f"   - الصلاحية: {new_user.user_role}")
                    print(f"   - لا يحتاج إلى مستودعات (يعمل على كشف الاستلامات وإدارة المواقع)")
            
            # تحديث المستخدمين الحاليين إذا لزم الأمر
            print("\n🔄 التحقق من تحديث المستخدمين الحاليين...")
            
            updated_users = 0
            for user in users:
                # التأكد من أن خاصية is_company_duty تعمل بشكل صحيح
                if hasattr(user, 'is_company_duty'):
                    if user.role == 'company_duty' or user.user_role == 'مناوب السرية':
                        # إزالة المستودعات من مناوب السرية (لا يحتاجها)
                        if user.warehouses:
                            user.warehouses.clear()
                            updated_users += 1
                            print(f"   ✅ تم إزالة المستودعات من المستخدم {user.username} (مناوب السرية لا يحتاجها)")
            
            if updated_users > 0:
                db.session.commit()
                print(f"✅ تم تحديث {updated_users} مستخدم")
            else:
                print("✅ جميع المستخدمين محدثين بالفعل")
            
            print("\n🎉 تم إضافة صلاحية مناوب السرية بنجاح!")
            print("\n📋 ملخص الصلاحيات:")
            print("   - مناوب السرية يمكنه الوصول إلى:")
            print("     ✅ كشف الاستلامات (قراءة، إضافة، تعديل، حذف)")
            print("     ✅ إدارة المواقع (قراءة، إضافة، تعديل، حذف)")
            print("     ❌ جميع الأقسام الأخرى مخفية")
            print("   - عند تسجيل الدخول يتم توجيهه مباشرة إلى كشف الاستلامات")
            
        except Exception as e:
            print(f"❌ خطأ في إضافة صلاحية مناوب السرية: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    add_company_duty_role()
