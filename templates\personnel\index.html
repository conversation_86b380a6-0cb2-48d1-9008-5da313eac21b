{% extends "base.html" %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/warehouse-list.css') }}">
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h3>
            قائمة الأفراد
        </h3>
    </div>
    <div class="col-md-4 text-right">
        <a href="{{ url_for('personnel.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة فرد جديد
        </a>
        <a href="{{ url_for('reports.export', export_type='personnel', warehouse_id=0) }}"
            class="btn btn-outline-secondary ml-2">
            <i class="fas fa-file-export"></i> تصدير
        </a>
    </div>
</div>

<div class="card mb-4" style="position: relative;z-index: 1;">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div class="me-0">
            <h5 class="mb-0"><i class="fas fa-users"></i> الأفراد</h5>
        </div>
        <div>
            <form action="{{ url_for('personnel.search') }}" method="GET" class="d-flex" id="searchForm">
                <input type="text" name="q" class="form-control ms-2" placeholder="بحث..." id="searchInput" autocomplete="off">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    <div class="card-body p-0">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>#</th>
                    <th>الرقم العسكري</th>
                    <th>الاسم</th>
                    <th>الرتبة</th>
                    <th>الحالة</th>
                    <th>المستودع</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for personnel in personnel_list %}
                <tr>
                    <td>{{ loop.index }}</td>
                    <td>{{ personnel.personnel_id }}</td>
                    <td>{{ personnel.name }}</td>
                    <td>{{ personnel.rank }}</td>
                    <td>
                        {% if personnel.status == 'نشط' %}
                        <span class="badge badge-success">{{ personnel.status }}</span>
                        {% elif personnel.status == 'إجازة' %}
                        <span class="badge badge-warning">{{ personnel.status }}</span>
                        {% elif personnel.status == 'مهمة' %}
                        <span class="badge badge-mission">{{ personnel.status }}</span>
                        {% elif personnel.status == 'دورة' %}
                        <span class="badge badge-danger">{{ personnel.status }}</span>
                        {% elif personnel.status == 'مستلم' %}
                        <span class="badge badge-primary">{{ personnel.status }}</span>
                        {% elif personnel.status == 'رماية' %}
                        <span class="badge badge-shooting">{{ personnel.status }}</span>
                        {% else %}
                        <span class="badge badge-secondary">{{ personnel.status }}</span>
                        {% endif %}
                    </td>
                    <td>{{ personnel.warehouse.name }}</td>
                    <td style="max-width: 110px;">
                        <div class="btn-box">
                            <a href="{{ url_for('personnel.details', personnel_id=personnel.id) }}"
                                class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{{ url_for('personnel.edit', personnel_id=personnel.id) }}"
                                class="btn btn-sm btn-outline-info">
                                <i class="fas fa-edit"></i>
                            </a>
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <div class="dropdown-menu dropdown-menu-right">
                                    <a class="dropdown-item"
                                        href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='نشط') }}">
                                        <i class="fas fa-check-circle text-success mr-2"></i> تعيين كنشط
                                    </a>
                                    <a class="dropdown-item"
                                        href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='إجازة') }}">
                                        <i class="fas fa-calendar text-warning mr-2"></i> تعيين في إجازة
                                    </a>
                                    <a class="dropdown-item"
                                        href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='مهمة') }}">
                                        <i class="fas fa-tasks text-warning mr-2" style="color: #fd7e14 !important;"></i> تعيين في مهمة
                                    </a>
                                    <a class="dropdown-item"
                                        href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='دورة') }}">
                                        <i class="fas fa-graduation-cap text-danger mr-2"></i> تعيين في دورة
                                    </a>
                                    <a class="dropdown-item"
                                        href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='رماية') }}">
                                        <i class="fas fa-bullseye mr-2" style="color: #C8BBBE !important;"></i> تعيين في رماية
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <a class="dropdown-item"
                                        href="{{ url_for('personnel.update_status', personnel_id=personnel.id, status='مستلم') }}">
                                        <i class="fas fa-user-clock text-primary mr-2"></i> تعيين كمستلم
                                    </a>
                                    <div class="dropdown-divider"></div>
                                    <form method="POST"
                                        action="{{ url_for('personnel.delete_personnel', personnel_id=personnel.id) }}">
                                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-trash text-danger mr-2"></i> حذف
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="7" class="text-center py-3">
                        <div class="alert alert-info mb-0">
                            <i class="fas fa-info-circle"></i> لا يوجد أفراد مسجلين حتى الآن
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        <!-- <div class="table-responsive" style="overflow: unset;">
        </div> -->
    </div>
</div>

{% if personnel_list %}
<div class="row">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">توزيع الأفراد حسب الحالة</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 250px;">
                    <canvas id="personnelStatusChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">توزيع الأفراد حسب المستودع</h5>
            </div>
            <div class="card-body">
                <div class="chart-container" style="position: relative; height: 250px;">
                    <canvas id="personnelWarehouseChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const searchForm = document.getElementById('searchForm');

        // تعيين التركيز على حقل البحث عند تحميل الصفحة ووضع المؤشر في نهاية النص
        searchInput.focus();
        // وضع المؤشر في نهاية النص
        const inputValue = searchInput.value;
        searchInput.value = '';
        searchInput.value = inputValue;

        // متغير لتخزين الوقت المستغرق بين الإدخالات
        let typingTimer;
        // الفترة الزمنية بالمللي ثانية قبل إرسال النموذج (300 مللي ثانية = 0.3 ثانية)
        const doneTypingInterval = 300;

        // عند الكتابة في حقل البحث
        searchInput.addEventListener('input', function() {
            // إعادة ضبط المؤقت في كل مرة يتم فيها الكتابة
            clearTimeout(typingTimer);

            // إذا كان الحقل غير فارغ، ابدأ المؤقت
            if (searchInput.value) {
                typingTimer = setTimeout(submitForm, doneTypingInterval);
            }
        });

        // وظيفة إرسال النموذج
        function submitForm() {
            searchForm.submit();
        }

        // عند مسح الحقل بالكامل، قم بإرسال النموذج أيضًا للعودة إلى القائمة الكاملة
        searchInput.addEventListener('keyup', function(e) {
            if (searchInput.value === '' && e.key === 'Backspace') {
                submitForm();
            }
        });
    });
</script>
{% if personnel_list %}
<script>
    document.addEventListener('DOMContentLoaded', function () {

        document.querySelectorAll('.dropdown-toggle').forEach((dropdown) => {
            dropdown.addEventListener('click', function (e) {
                e.preventDefault();
                e.stopPropagation(); // Stop Bootstrap's listener if still active
                const isOpen = dropdown.classList.contains("show");
                // Close all other dropdowns
                document.querySelectorAll(".dropdown-toggle").forEach((el) => {
                    el.classList.remove("show");
                    el.setAttribute("aria-expanded", false);
                });
                document.querySelectorAll(".dropdown-menu").forEach((el) => {
                    el.classList.remove("show");
                });

                if (!isOpen) {
                    dropdown.classList.add("show");
                } else {
                    dropdown.classList.remove("show");
                }
                dropdown.setAttribute("aria-expanded", dropdown.classList.contains("show"));

                document.addEventListener("click", function closeDropDown(event) {
                    // إغلاق القوائم المنسدلة عند النقر في أي مكان آخر

                    if (!dropdown.contains(event.target) || dropdown !== event.target) {
                        dropdown.classList.remove("show");
                        dropdown.setAttribute("aria-expanded", false);
                        document.removeEventListener("click", closeDropDown);
                    }
                });
            });

        });


        // Count personnel by status
        let activeCount = 0;
        let leaveCount = 0;
        let missionCount = 0;
        let cycleCount = 0;
        let retiredCount = 0;
        let shootingCount = 0;

        // Count personnel by warehouse
        const warehouseCounts = {};
        const warehouseNames = {};

        {% for personnel in personnel_list %}
        {% if personnel.status == 'نشط' %}
        activeCount++;
        {% elif personnel.status == 'إجازة' %}
        leaveCount++;
        {% elif personnel.status == 'مهمة' %}
        missionCount++;
        {% elif personnel.status == 'دورة' %}
        cycleCount++;
        {% elif personnel.status == 'مستلم' %}
        retiredCount++;
        {% elif personnel.status == 'رماية' %}
        shootingCount++;
        {% endif %}

        var wid = "{{ personnel.warehouse_id }}";
        if (!warehouseCounts[wid]) {
            warehouseCounts[wid] = 0;
            warehouseNames[wid] = "{{ personnel.warehouse.name }}";
        }
        warehouseCounts[wid]++;
        {% endfor %}

        // Status Chart
        const statusCtx = document.getElementById('personnelStatusChart').getContext('2d');
        new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['نشط', 'إجازة', 'مهمة', 'دورة', 'مستلم', 'رماية'],
                datasets: [{
                    data: [activeCount, leaveCount, missionCount, cycleCount, retiredCount, shootingCount],
                    backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545', '#007bff', '#C8BBBE']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                family: 'Cairo, Tajawal, sans-serif'
                            },
                            color: document.body.classList.contains('light-theme') ? '#212529' : '#f0f0f0'
                        }
                    }
                }
            }
        });

        // Warehouse Chart
        const warehouseCtx = document.getElementById('personnelWarehouseChart').getContext('2d');
        new Chart(warehouseCtx, {
            type: 'pie',
            data: {
                labels: Object.values(warehouseNames),
                datasets: [{
                    data: Object.values(warehouseCounts),
                    backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6c757d']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                family: 'Cairo, Tajawal, sans-serif'
                            },
                            color: document.body.classList.contains('light-theme') ? '#212529' : '#f0f0f0'
                        }
                    }
                }
            }
        });
    });
</script>
{% endif %}

{% endblock %}