#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import User

def check_users():
    app = create_app()
    with app.app_context():
        users = User.query.all()
        print(f"عدد المستخدمين: {len(users)}")
        for user in users:
            print(f"\nالمستخدم: {user.username}")
            print(f"role: {user.role}")
            print(f"user_role: {user.user_role}")
            print(f"is_company_duty: {user.is_company_duty}")

if __name__ == "__main__":
    check_users()
