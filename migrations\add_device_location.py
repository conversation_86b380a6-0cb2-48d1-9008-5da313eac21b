#!/usr/bin/env python3
"""
Migration script to add location column to devices table
"""

import os
import sys
from sqlalchemy import text

# Add the parent directory to the path so we can import the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from db import db

def upgrade():
    """Add location column to devices table."""
    app = create_app()
    
    with app.app_context():
        try:
            # Add location column to devices table
            sql = text("""
            ALTER TABLE devices
            ADD COLUMN IF NOT EXISTS location VARCHAR(200);
            """)
            
            with db.engine.connect() as conn:
                conn.execute(sql)
                conn.commit()
            
            print("✅ Migration completed successfully!")
            print("✅ Added location column to devices table")
            
        except Exception as e:
            print(f"❌ Migration failed: {str(e)}")
            raise

if __name__ == "__main__":
    upgrade()
