<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البحث</title>
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <style>
        body { font-family: 'Tajawal', sans-serif; padding: 20px; }
        .result { margin-top: 20px; padding: 15px; border-radius: 5px; }
        .success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار البحث عن الأفراد</h1>
        
        <div class="card">
            <div class="card-body">
                <div class="mb-3">
                    <label for="searchTerm" class="form-label">رقم الهوية الوطنية أو الرقم العسكري</label>
                    <input type="text" class="form-control" id="searchTerm" placeholder="أدخل الرقم">
                </div>
                
                <button type="button" class="btn btn-primary" onclick="testSearch()">بحث</button>
                <button type="button" class="btn btn-secondary" onclick="clearResults()">مسح</button>
            </div>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        function testSearch() {
            const searchTerm = document.getElementById('searchTerm').value.trim();
            const resultsDiv = document.getElementById('results');
            
            if (!searchTerm) {
                showResult('يرجى إدخال رقم البحث', 'error');
                return;
            }
            
            // إظهار رسالة تحميل
            resultsDiv.innerHTML = '<div class="result">جاري البحث...</div>';
            
            // إرسال طلب البحث
            fetch(`/locations/81/personnel/search?search_term=${encodeURIComponent(searchTerm)}`)
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers.get('content-type'));
                    
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error('الاستجابة ليست JSON - تحتاج لتسجيل الدخول');
                    }
                    
                    return response.json();
                })
                .then(data => {
                    console.log('Search response:', data);
                    
                    if (data.success && data.personnel) {
                        const personnel = data.personnel;
                        const resultHtml = `
                            <div class="result success">
                                <h5>✅ تم العثور على الفرد</h5>
                                <p><strong>الاسم:</strong> ${personnel.name}</p>
                                <p><strong>الرقم العسكري:</strong> ${personnel.personnel_id}</p>
                                <p><strong>رقم الهوية:</strong> ${personnel.phone}</p>
                                <p><strong>الرتبة:</strong> ${personnel.rank}</p>
                                <p><strong>الحالة:</strong> ${personnel.status}</p>
                                <p><strong>الوحدة:</strong> ${personnel.warehouse_name}</p>
                                <p><strong>معرف الفرد:</strong> ${personnel.id}</p>
                            </div>
                        `;
                        resultsDiv.innerHTML = resultHtml;
                    } else {
                        showResult(`❌ ${data.message || 'لم يتم العثور على فرد بهذا الرقم'}`, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showResult(`❌ خطأ: ${error.message}`, 'error');
                });
        }
        
        function showResult(message, type) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('searchTerm').value = '';
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.onload = function() {
            console.log('صفحة اختبار البحث جاهزة');
        };
    </script>
</body>
</html>
