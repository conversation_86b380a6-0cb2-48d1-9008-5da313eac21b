@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo.
echo    ================================================
echo    ATAD System
echo    ================================================
echo.

REM Check Python
python --version > nul 2>&1
if errorlevel 1 (
    echo X Python not found. Please install Python 3.8 or newer
    pause
    exit /b 1
)

REM Create virtual environment if not exists
IF NOT EXIST "venv" (
    echo - Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
echo - Activating virtual environment...
call venv\Scripts\activate.bat

REM Install requirements
echo - Installing requirements...
pip install -r requirements.txt

REM Set database environment variables
set DATABASE_URL=sqlite:///military_warehouse.db

echo.
echo - Starting system...
echo.
echo URL: http://localhost:5000
echo Username: admin
echo Password: admin123
echo.
echo Press Ctrl+C to stop the system
echo.

REM Run the system
python app.py

echo.
echo System stopped
pause
