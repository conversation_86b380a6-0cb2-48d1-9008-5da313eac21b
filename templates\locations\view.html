{% extends "base.html" %}

{% block title %}عرض الموقع - {{ location.name }}{% endblock %}

{% block styles %}
<style>
    .location-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    }

    .location-title {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .location-subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin: 0;
    }

    .info-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .info-card h3 {
        color: var(--primary-color);
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid var(--primary-color);
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 15px;
        background: var(--section-bg);
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }

    .info-icon {
        width: 40px;
        height: 40px;
        background: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.1rem;
    }

    .info-content {
        flex: 1;
    }

    .info-label {
        font-size: 0.9rem;
        color: var(--text-secondary);
        margin-bottom: 5px;
    }

    .info-value {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        text-align: center;
        display: inline-block;
    }

    .status-active { background: #d4edda; color: #155724; }
    .status-inactive { background: #f8d7da; color: #721c24; }
    .status-withdrawn { background: #fff3cd; color: #856404; }
    .status-maintenance { background: #cce7ff; color: #004085; }

    .equipment-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }

    .equipment-item {
        background: var(--section-bg);
        border: 1px solid var(--border-color);
        border-radius: 10px;
        padding: 20px;
        transition: all 0.3s ease;
    }

    .equipment-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .equipment-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 15px;
    }

    .equipment-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--primary-color);
        margin: 0;
    }

    .equipment-type {
        background: var(--primary-color);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        margin-left: 10px;
    }

    .equipment-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin-top: 15px;
    }

    .equipment-detail {
        font-size: 0.9rem;
    }

    .equipment-detail strong {
        color: var(--text-primary);
    }

    .condition-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .condition-good { background: #d4edda; color: #155724; }
    .condition-average { background: #fff3cd; color: #856404; }
    .condition-needs-repair { background: #f8d7da; color: #721c24; }
    .condition-broken { background: #f5c6cb; color: #721c24; }

    .action-buttons {
        display: flex;
        gap: 15px;
        margin-top: 30px;
    }

    .btn-action {
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-edit {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
    }

    .btn-delete {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        border: none;
    }

    .btn-back {
        background: linear-gradient(135deg, #6c757d, #5a6268);
        color: white;
        border: none;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        color: white;
        text-decoration: none;
    }

    .empty-equipment {
        text-align: center;
        padding: 40px;
        color: var(--text-secondary);
    }

    .empty-equipment i {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    @media (max-width: 768px) {
        .location-header {
            padding: 20px;
        }
        
        .location-title {
            font-size: 1.5rem;
        }
        
        .info-grid {
            grid-template-columns: 1fr;
        }
        
        .equipment-grid {
            grid-template-columns: 1fr;
        }
        
        .action-buttons {
            flex-direction: column;
        }
        
        .equipment-details {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Location Header -->
    <div class="location-header">
        <h1 class="location-title">{{ location.name }}</h1>
        <p class="location-subtitle">الرقم التسلسلي: {{ location.serial_number }}</p>
    </div>

    <div class="row">
        <!-- Basic Information -->
        <div class="col-lg-8">
            <div class="info-card">
                <h3><i class="fas fa-info-circle"></i> المعلومات الأساسية</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-tag"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">نوع الموقع</div>
                            <div class="info-value">{{ location.type }}</div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-circle-check"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">حالة الموقع</div>
                            <div class="info-value">
                                <span class="status-badge status-{{ 'active' if location.status == 'نشط' else 'inactive' if location.status == 'غير نشط' else 'withdrawn' if location.status == 'مسحوب' else 'maintenance' }}">
                                    {{ location.status }}
                                </span>
                            </div>
                        </div>
                    </div>

                    {% if location.coordinates %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-map-pin"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">الإحداثيات</div>
                            <div class="info-value">{{ location.coordinates }}</div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">تاريخ الإنشاء</div>
                            <div class="info-value">{{ location.created_at[:10] if location.created_at else 'غير محدد' }}</div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">عدد العهد</div>
                            <div class="info-value">{{ location.equipment|length if location.equipment else 0 }} عهدة</div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">عدد الأفراد</div>
                            <div class="info-value">{{ location.personnel|length if location.personnel else 0 }} فرد</div>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-hashtag"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">معرف الموقع</div>
                            <div class="info-value">#{{ location.id }}</div>
                        </div>
                    </div>
                </div>

                {% if location.description %}
                <div class="mt-4">
                    <h5><i class="fas fa-file-text"></i> الوصف</h5>
                    <p class="text-muted">{{ location.description }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="col-lg-4">
            <div class="info-card">
                <h3><i class="fas fa-chart-bar"></i> إحصائيات سريعة</h3>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>العهد الجيدة</span>
                        <span class="badge bg-light text-dark border">{{ location.equipment|selectattr('condition_status', 'equalto', 'جيد')|list|length }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>تحتاج صيانة</span>
                        <span class="badge bg-light text-dark border">{{ location.equipment|selectattr('condition_status', 'equalto', 'يحتاج صيانة')|list|length }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>معطلة</span>
                        <span class="badge bg-light text-dark border">{{ location.equipment|selectattr('condition_status', 'equalto', 'معطل')|list|length }}</span>
                    </div>
                </div>

                <hr>

                <div class="text-center">
                    <h4 class="text-primary">{{ location.equipment|length if location.equipment else 0 }}</h4>
                    <p class="text-muted mb-0">إجمالي العهد</p>
                </div>

                <hr>

                <div class="text-center">
                    <h4 class="text-success">{{ location.personnel|length if location.personnel else 0 }}</h4>
                    <p class="text-muted mb-0">الأفراد المرتبطين</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Equipment Section -->
    <div class="info-card">
        <h3><i class="fas fa-boxes"></i> العهد المخصصة للموقع</h3>
        
        {% if location.equipment %}
            <div class="equipment-grid">
                {% for equipment in location.equipment %}
                <div class="equipment-item">
                    <div class="equipment-header">
                        <h4 class="equipment-name">{{ equipment.equipment_name }}</h4>
                        {% if equipment.equipment_type %}
                        <span class="equipment-type">{{ equipment.equipment_type }}</span>
                        {% endif %}
                    </div>
                    
                    <div class="equipment-details">
                        {% if equipment.serial_number %}
                        <div class="equipment-detail">
                            <strong>الرقم التسلسلي:</strong><br>
                            {{ equipment.serial_number }}
                        </div>
                        {% endif %}
                        
                        <div class="equipment-detail">
                            <strong>الكمية:</strong><br>
                            {{ equipment.quantity }}
                        </div>
                        
                        <div class="equipment-detail">
                            <strong>الحالة:</strong><br>
                            <span class="condition-badge condition-{{ 'good' if equipment.condition_status == 'جيد' else 'average' if equipment.condition_status == 'متوسط' else 'needs-repair' if equipment.condition_status == 'يحتاج صيانة' else 'broken' }}">
                                {{ equipment.condition_status }}
                            </span>
                        </div>
                        
                        {% if equipment.notes %}
                        <div class="equipment-detail">
                            <strong>ملاحظات:</strong><br>
                            {{ equipment.notes }}
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-equipment">
                <i class="fas fa-box-open"></i>
                <h4>لا توجد عهد مخصصة</h4>
                <p>لم يتم تخصيص أي عهد لهذا الموقع بعد.</p>
            </div>
        {% endif %}
    </div>

    <!-- Personnel Section -->
    <div class="info-card">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3><i class="fas fa-users"></i> الأفراد المرتبطين بالموقع</h3>
            <button type="button" class="btn btn-primary" onclick="openAddPersonnelModal()">
                <i class="fas fa-plus"></i> إضافة فرد
            </button>
        </div>

        {% if location.personnel %}
            <div class="row">
                {% for person in location.personnel %}
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="card-title mb-1">{{ person.name }}</h5>
                                <button type="button" class="btn btn-sm btn-outline-danger"
                                        onclick="removePersonnel({{ person.id }}, '{{ person.name }}')"
                                        title="إزالة من الموقع">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>

                            <div class="mb-2">
                                <small class="text-muted">الرقم العسكري:</small>
                                <div class="fw-bold">{{ person.personnel_id }}</div>
                            </div>

                            {% if person.rank %}
                            <div class="mb-2">
                                <small class="text-muted">الرتبة:</small>
                                <div>{{ person.rank }}</div>
                            </div>
                            {% endif %}

                            <div class="mb-2">
                                <small class="text-muted">الحالة:</small>
                                <div>
                                    <span class="badge bg-light text-dark border">
                                        {{ person.status }}
                                    </span>
                                </div>
                            </div>

                            {% if person.shift_type and person.shift_type != 'عام' %}
                            <div class="mb-2">
                                <small class="text-muted">نوع الوردية:</small>
                                <div>{{ person.shift_type }}</div>
                            </div>
                            {% endif %}

                            <div class="mb-2">
                                <small class="text-muted">الوحدة:</small>
                                <div>{{ person.warehouse_name }}</div>
                            </div>

                            {% if person.assignment_date %}
                            <div class="mb-2">
                                <small class="text-muted">تاريخ التعيين:</small>
                                <div>{{ person.assignment_date[:10] }}</div>
                            </div>
                            {% endif %}

                            {% if person.notes %}
                            <div class="mt-2">
                                <small class="text-muted">ملاحظات:</small>
                                <div class="small">{{ person.notes }}</div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-equipment">
                <i class="fas fa-user-plus"></i>
                <h4>لا يوجد أفراد مرتبطين</h4>
                <p>لم يتم تعيين أي أفراد لهذا الموقع بعد.</p>
                <button type="button" class="btn btn-primary mt-3" onclick="openAddPersonnelModal()">
                    <i class="fas fa-plus"></i> إضافة أول فرد
                </button>
            </div>
        {% endif %}
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{{ url_for('locations.index') }}" class="btn-action btn-back">
            <i class="fas fa-arrow-right"></i>
            العودة للقائمة
        </a>
        <a href="{{ url_for('locations.edit_location', location_id=location.id) }}" class="btn-action btn-edit">
            <i class="fas fa-edit"></i>
            تعديل الموقع
        </a>
        <button type="button" class="btn-action btn-delete" onclick="deleteLocation({{ location.id }}, '{{ location.name }}')">
            <i class="fas fa-trash"></i>
            حذف الموقع
        </button>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الموقع "<span id="locationNameToDelete"></span>"؟</p>
                <p class="text-danger"><small>سيتم حذف جميع العهد المرتبطة بهذا الموقع أيضاً.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">حذف</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Personnel Modal -->
<div class="modal fade" id="addPersonnelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة فرد للموقع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addPersonnelForm">
                    <div class="mb-3">
                        <label for="searchTerm" class="form-label">رقم الهوية الوطنية أو الرقم العسكري</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchTerm" placeholder="أدخل رقم الهوية أو الرقم العسكري">
                            <button type="button" class="btn btn-outline-primary" onclick="searchPersonnel()">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>

                    <div id="personnelSearchResult" style="display: none;">
                        <div class="alert alert-info">
                            <h6>بيانات الفرد:</h6>
                            <div id="personnelInfo"></div>
                        </div>

                        <div class="mb-3">
                            <label for="shiftType" class="form-label">نوع الوردية</label>
                            <select class="form-select" id="shiftType">
                                <option value="عام">عام</option>
                                <option value="صباحية">صباحية</option>
                                <option value="مسائية">مسائية</option>
                                <option value="ليلية">ليلية</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="assignmentNotes" class="form-label">ملاحظات</label>
                            <textarea class="form-control" id="assignmentNotes" rows="3" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                        </div>
                    </div>

                    <div id="searchError" class="alert alert-danger" style="display: none;"></div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="addPersonnelBtn" onclick="addPersonnelToLocation()" style="display: none;">
                    <i class="fas fa-plus"></i> إضافة للموقع
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Remove Personnel Confirmation Modal -->
<div class="modal fade" id="removePersonnelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد إزالة الفرد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إزالة الفرد "<span id="personnelNameToRemove"></span>" من هذا الموقع؟</p>
                <p class="text-warning"><small>سيتم إزالة الفرد من الموقع فقط وليس من النظام.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmRemovePersonnel">إزالة</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPersonnelData = null;

function openAddPersonnelModal() {
    const modal = new bootstrap.Modal(document.getElementById('addPersonnelModal'));
    modal.show();

    // Reset form
    document.getElementById('addPersonnelForm').reset();
    document.getElementById('personnelSearchResult').style.display = 'none';
    document.getElementById('searchError').style.display = 'none';
    document.getElementById('addPersonnelBtn').style.display = 'none';
    currentPersonnelData = null;
}

function searchPersonnel() {
    const searchTerm = document.getElementById('searchTerm').value.trim();

    if (!searchTerm) {
        showSearchError('يرجى إدخال رقم الهوية أو الرقم العسكري');
        return;
    }

    // Show loading
    const searchBtn = document.querySelector('#addPersonnelModal .btn-outline-primary');
    const originalText = searchBtn.innerHTML;
    searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري البحث...';
    searchBtn.disabled = true;

    fetch(`/locations/{{ location.id }}/personnel/search?search_term=${encodeURIComponent(searchTerm)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showPersonnelInfo(data.personnel);
                currentPersonnelData = data.personnel;
            } else {
                showSearchError(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showSearchError('حدث خطأ أثناء البحث');
        })
        .finally(() => {
            searchBtn.innerHTML = originalText;
            searchBtn.disabled = false;
        });
}

function showPersonnelInfo(personnel) {
    const infoDiv = document.getElementById('personnelInfo');
    infoDiv.innerHTML = `
        <strong>الاسم:</strong> ${personnel.name}<br>
        <strong>الرقم العسكري:</strong> ${personnel.personnel_id}<br>
        <strong>الرتبة:</strong> ${personnel.rank || 'غير محدد'}<br>
        <strong>الحالة:</strong> ${personnel.status}<br>
        <strong>الوحدة:</strong> ${personnel.warehouse_name}
    `;

    document.getElementById('personnelSearchResult').style.display = 'block';
    document.getElementById('searchError').style.display = 'none';
    document.getElementById('addPersonnelBtn').style.display = 'block';
}

function showSearchError(message) {
    document.getElementById('searchError').textContent = message;
    document.getElementById('searchError').style.display = 'block';
    document.getElementById('personnelSearchResult').style.display = 'none';
    document.getElementById('addPersonnelBtn').style.display = 'none';
}

function addPersonnelToLocation() {
    if (!currentPersonnelData) {
        showSearchError('لم يتم العثور على بيانات الفرد');
        return;
    }

    const shiftType = document.getElementById('shiftType').value;
    const notes = document.getElementById('assignmentNotes').value.trim();

    const addBtn = document.getElementById('addPersonnelBtn');
    const originalText = addBtn.innerHTML;
    addBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإضافة...';
    addBtn.disabled = true;

    fetch(`/locations/{{ location.id }}/personnel/add`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            personnel_id: currentPersonnelData.id,
            shift_type: shiftType,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إضافة الفرد للموقع بنجاح', 'success');
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showNotification(data.message || 'حدث خطأ أثناء إضافة الفرد', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ أثناء إضافة الفرد', 'error');
    })
    .finally(() => {
        addBtn.innerHTML = originalText;
        addBtn.disabled = false;
    });
}

function removePersonnel(personnelId, personnelName) {
    document.getElementById('personnelNameToRemove').textContent = personnelName;
    const modal = new bootstrap.Modal(document.getElementById('removePersonnelModal'));
    modal.show();

    document.getElementById('confirmRemovePersonnel').onclick = function() {
        fetch(`/locations/{{ location.id }}/personnel/${personnelId}/remove`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('تم إزالة الفرد من الموقع بنجاح', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showNotification(data.message || 'حدث خطأ أثناء إزالة الفرد', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('حدث خطأ أثناء إزالة الفرد', 'error');
        });

        modal.hide();
    };
}

// Allow Enter key to trigger search
document.getElementById('searchTerm').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
        searchPersonnel();
    }
});
</script>

<script>
function deleteLocation(id, name) {
    document.getElementById('locationNameToDelete').textContent = name;
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
    
    document.getElementById('confirmDelete').onclick = function() {
        fetch(`/locations/${id}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('تم حذف الموقع بنجاح', 'success');
                setTimeout(() => {
                    window.location.href = "{{ url_for('locations.index') }}";
                }, 1500);
            } else {
                showNotification(data.message || 'حدث خطأ أثناء حذف الموقع', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('حدث خطأ أثناء حذف الموقع', 'error');
        });
        
        modal.hide();
    };
}
</script>
{% endblock %}
