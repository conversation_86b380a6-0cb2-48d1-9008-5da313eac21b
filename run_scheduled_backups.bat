@echo off
REM ===================================================================
REM ملف تشغيل النسخ الاحتياطية المجدولة
REM Scheduled Backup Runner for Windows
REM ===================================================================

REM تعيين مسار المشروع (يجب تعديله حسب موقع المشروع)
set PROJECT_PATH=%~dp0

REM الانتقال إلى مجلد المشروع
cd /d "%PROJECT_PATH%"

REM تسجيل بداية العملية
echo [%date% %time%] بدء تشغيل النسخ الاحتياطية المجدولة >> scheduler.log

REM تشغيل خدمة الجدولة (فحص واحد)
python scheduler_service.py --once >> scheduler.log 2>&1

REM تسجيل انتهاء العملية
echo [%date% %time%] انتهاء تشغيل النسخ الاحتياطية المجدولة >> scheduler.log
echo. >> scheduler.log

REM إنهاء الملف
exit /b 0
