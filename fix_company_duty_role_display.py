#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح عرض صلاحية مناوب السرية
Fix Company Duty role display

هذا الملف يقوم بإصلاح عرض صلاحية مناوب السرية في قائمة المستخدمين
ويتأكد من أن النص العربي يظهر بشكل صحيح
"""

import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from db import db
from models import User
from sqlalchemy import or_

def fix_company_duty_role_display():
    """إصلاح عرض صلاحية مناوب السرية"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 بدء إصلاح عرض صلاحية مناوب السرية...")
            
            # البحث عن جميع مستخدمي مناوب السرية
            company_duty_users = User.query.filter(
                or_(User.role == 'company_duty', User.user_role == 'مناوب السرية')
            ).all()
            
            if not company_duty_users:
                print("ℹ️  لا يوجد مستخدمين بصلاحية مناوب السرية")
                return
            
            print(f"📊 تم العثور على {len(company_duty_users)} مستخدم بصلاحية مناوب السرية:")
            
            updated_users = 0
            for user in company_duty_users:
                print(f"\n👤 المستخدم: {user.username}")
                print(f"   - الدور الحالي (role): {user.role}")
                print(f"   - النص الحالي (user_role): {user.user_role}")
                
                # التحقق من الحاجة للتحديث
                needs_update = False
                
                # إذا كان role صحيح ولكن user_role خطأ
                if user.role == 'company_duty' and user.user_role != 'مناوب السرية':
                    user.user_role = 'مناوب السرية'
                    needs_update = True
                    print(f"   ✅ تم تحديث النص إلى: مناوب السرية")
                
                # إذا كان user_role صحيح ولكن role خطأ
                elif user.user_role == 'مناوب السرية' and user.role != 'company_duty':
                    user.role = 'company_duty'
                    needs_update = True
                    print(f"   ✅ تم تحديث الدور إلى: company_duty")
                
                # التأكد من أن is_admin = False
                if user.is_admin:
                    user.is_admin = False
                    needs_update = True
                    print(f"   ✅ تم تحديث is_admin إلى: False")
                
                if needs_update:
                    updated_users += 1
                    print(f"   🔄 المستخدم محدث")
                else:
                    print(f"   ✅ المستخدم صحيح بالفعل")
                
                # عرض الحالة النهائية
                print(f"   📋 الحالة النهائية:")
                print(f"      - role: {user.role}")
                print(f"      - user_role: {user.user_role}")
                print(f"      - is_admin: {user.is_admin}")
                print(f"      - is_company_duty: {user.is_company_duty}")
            
            if updated_users > 0:
                # حفظ التغييرات
                db.session.commit()
                print(f"\n✅ تم تحديث {updated_users} مستخدم بنجاح")
            else:
                print(f"\n✅ جميع المستخدمين صحيحين بالفعل")
            
            print("\n🎉 تم إصلاح عرض صلاحية مناوب السرية بنجاح!")
            
            # اختبار العرض
            print("\n🧪 اختبار العرض:")
            for user in company_duty_users:
                display_role = ""
                if user.role == 'admin' or user.is_admin:
                    display_role = "مدير نظام"
                elif user.role == 'warehouse_manager':
                    display_role = "مدير مستودع"
                elif user.role == 'inventory_manager':
                    display_role = "مسؤول مخزون"
                elif user.role == 'company_duty' or user.user_role == 'مناوب السرية':
                    display_role = "مناوب السرية"
                else:
                    display_role = "مراقب"
                
                print(f"   - {user.username}: {display_role}")
            
        except Exception as e:
            print(f"❌ خطأ في إصلاح عرض الصلاحية: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    fix_company_duty_role_display()
