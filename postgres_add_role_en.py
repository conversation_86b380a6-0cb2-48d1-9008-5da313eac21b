#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة عمود role_en لـ PostgreSQL
Add role_en column for PostgreSQL
"""

import psycopg2
import os

def add_role_en_column():
    """إضافة عمود role_en مباشرة إلى قاعدة البيانات PostgreSQL"""
    
    try:
        print("🔄 الاتصال بقاعدة البيانات PostgreSQL...")
        
        # الاتصال بقاعدة البيانات
        conn = psycopg2.connect(
            host="localhost",
            database="military_warehouse",
            user="postgres",
            password="postgres",
            port="5432"
        )
        
        cursor = conn.cursor()
        
        # التحقق من وجود العمود
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name = 'role_en'
        """)
        
        if cursor.fetchone() is None:
            print("📝 إضافة عمود role_en...")
            cursor.execute("ALTER TABLE users ADD COLUMN role_en VARCHAR(50)")
            print("✅ تم إضافة عمود role_en بنجاح")
        else:
            print("✅ عمود role_en موجود بالفعل")
        
        # تحديث البيانات الموجودة
        print("\n🔄 تحديث البيانات الموجودة...")
        
        # جلب جميع المستخدمين
        cursor.execute("SELECT id, username, user_role, is_admin FROM users")
        users = cursor.fetchall()
        
        print(f"📊 عدد المستخدمين: {len(users)}")
        
        arabic_to_english = {
            'مدير نظام': 'admin',
            'مدير المستودعات': 'warehouse_manager',
            'مسؤول مخزون': 'inventory_manager',
            'مراقب': 'monitor',
            'مناوب السرية': 'company_duty'
        }
        
        updated_users = 0
        for user_id, username, user_role, is_admin in users:
            print(f"\n👤 المستخدم: {username}")
            print(f"   - user_role: {user_role}")
            print(f"   - is_admin: {is_admin}")
            
            # تحديد الدور الإنجليزي
            if user_role and user_role in arabic_to_english:
                new_role_en = arabic_to_english[user_role]
            elif is_admin:
                new_role_en = 'admin'
                # تحديث user_role إذا لم يكن محدد
                if not user_role:
                    cursor.execute("UPDATE users SET user_role = %s WHERE id = %s", ('مدير نظام', user_id))
            else:
                new_role_en = 'monitor'
                # تحديث user_role إذا لم يكن محدد
                if not user_role:
                    cursor.execute("UPDATE users SET user_role = %s WHERE id = %s", ('مراقب', user_id))
            
            # تحديث role_en
            cursor.execute("UPDATE users SET role_en = %s WHERE id = %s", (new_role_en, user_id))
            
            # التأكد من is_admin
            if new_role_en == 'admin':
                cursor.execute("UPDATE users SET is_admin = true WHERE id = %s", (user_id,))
            else:
                cursor.execute("UPDATE users SET is_admin = false WHERE id = %s", (user_id,))
            
            updated_users += 1
            print(f"   - role_en: {new_role_en}")
        
        # حفظ التغييرات
        conn.commit()
        print(f"\n✅ تم تحديث {updated_users} مستخدم بنجاح")
        
        # عرض النتائج النهائية
        print("\n🧪 النتائج النهائية:")
        cursor.execute("SELECT username, user_role, role_en, is_admin FROM users")
        final_users = cursor.fetchall()
        
        for username, user_role, role_en, is_admin in final_users:
            print(f"   - {username}: {user_role} ({role_en}) [admin: {is_admin}]")
        
        cursor.close()
        conn.close()
        print("\n🎉 تم إصلاح عرض الأدوار بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    add_role_en_column()
