#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت للتحقق من تطابق ملفات السندات مع قاعدة البيانات
"""

import psycopg2
import os
import re

def check_documents_sync():
    """التحقق من تطابق ملفات السندات مع قاعدة البيانات"""
    
    try:
        # الاتصال بقاعدة البيانات
        conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
        cursor = conn.cursor()
        
        print("🔍 فحص تطابق ملفات السندات مع قاعدة البيانات...")
        
        # الحصول على جميع الأسلحة من قاعدة البيانات
        cursor.execute('''
            SELECT id, serial_number, weapon_document 
            FROM weapons 
            ORDER BY id
        ''')
        
        weapons = cursor.fetchall()
        print(f"📊 إجمالي الأسلحة في قاعدة البيانات: {len(weapons)}")
        
        # الحصول على ملفات السندات من المجلد
        documents_dir = 'static/uploads/weapon_documents'
        if os.path.exists(documents_dir):
            files = os.listdir(documents_dir)
            print(f"📁 إجمالي ملفات السندات في المجلد: {len(files)}")
        else:
            print("❌ مجلد السندات غير موجود!")
            return
        
        # تحليل الملفات
        weapons_with_db_docs = 0
        weapons_with_file_docs = 0
        missing_files = []
        orphaned_files = []
        
        # إنشاء قاموس للأسلحة حسب الرقم التسلسلي
        weapons_dict = {}
        for weapon_id, serial, doc_path in weapons:
            weapons_dict[serial] = {
                'id': weapon_id,
                'document': doc_path,
                'has_file': False
            }
            if doc_path:
                weapons_with_db_docs += 1
        
        # فحص الملفات الموجودة
        for file in files:
            # استخراج الرقم التسلسلي من اسم الملف
            # نمط: weapon_G-K4S1B-3480-0.091001634_hash.pdf
            match = re.search(r'weapon_(.+?)_[a-f0-9]+\.(pdf|jpg|jpeg|png|doc|docx)$', file)
            if match:
                serial_from_file = match.group(1)
                
                # البحث عن السلاح في قاعدة البيانات
                found = False
                for serial in weapons_dict:
                    if serial == serial_from_file:
                        weapons_dict[serial]['has_file'] = True
                        weapons_with_file_docs += 1
                        found = True
                        break
                
                if not found:
                    orphaned_files.append(file)
            else:
                print(f"⚠️  ملف بتنسيق غير متوقع: {file}")
        
        # فحص الملفات المفقودة
        for serial, data in weapons_dict.items():
            if data['document'] and not data['has_file']:
                missing_files.append({
                    'serial': serial,
                    'id': data['id'],
                    'document': data['document']
                })
        
        # طباعة النتائج
        print(f"\n📋 نتائج الفحص:")
        print(f"   - أسلحة لها سندات في قاعدة البيانات: {weapons_with_db_docs}")
        print(f"   - أسلحة لها ملفات سندات فعلية: {weapons_with_file_docs}")
        print(f"   - ملفات مفقودة: {len(missing_files)}")
        print(f"   - ملفات يتيمة (بدون سلاح): {len(orphaned_files)}")
        
        if missing_files:
            print(f"\n❌ ملفات مفقودة ({len(missing_files)}):")
            for item in missing_files[:10]:  # أول 10 فقط
                print(f"   - السلاح {item['id']}: {item['serial']}")
                print(f"     المسار المتوقع: {item['document']}")
        
        if orphaned_files:
            print(f"\n⚠️  ملفات يتيمة ({len(orphaned_files)}):")
            for file in orphaned_files[:10]:  # أول 10 فقط
                print(f"   - {file}")
        
        # اقتراح حلول
        print(f"\n💡 اقتراحات:")
        
        if len(orphaned_files) > 0:
            print("   1. ربط الملفات اليتيمة بالأسلحة المناسبة")
            print("   2. تحديث قاعدة البيانات لتشمل مسارات الملفات الموجودة")
        
        if len(missing_files) > 0:
            print("   3. إزالة مسارات الملفات المفقودة من قاعدة البيانات")
        
        # عرض عينة من الأسلحة التي لها ملفات
        print(f"\n✅ عينة من الأسلحة التي لها ملفات سندات:")
        count = 0
        for serial, data in weapons_dict.items():
            if data['has_file'] and data['document']:
                print(f"   - {serial} (ID: {data['id']})")
                count += 1
                if count >= 5:
                    break
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الفحص: {str(e)}")

def update_missing_documents():
    """تحديث قاعدة البيانات لربط الملفات الموجودة بالأسلحة"""
    
    try:
        conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
        cursor = conn.cursor()
        
        print("🔄 تحديث قاعدة البيانات لربط الملفات...")
        
        # الحصول على الملفات
        documents_dir = 'static/uploads/weapon_documents'
        files = os.listdir(documents_dir)
        
        updated_count = 0
        
        for file in files:
            # استخراج الرقم التسلسلي من اسم الملف
            match = re.search(r'weapon_(.+?)_[a-f0-9]+\.(pdf|jpg|jpeg|png|doc|docx)$', file)
            if match:
                serial_from_file = match.group(1)
                file_path = f"static/uploads/weapon_documents/{file}"
                
                # البحث عن السلاح في قاعدة البيانات
                cursor.execute('''
                    SELECT id, weapon_document 
                    FROM weapons 
                    WHERE serial_number = %s
                ''', (serial_from_file,))
                
                weapon = cursor.fetchone()
                if weapon:
                    weapon_id, current_doc = weapon
                    
                    # إذا لم يكن له سند مسجل، أضف المسار
                    if not current_doc:
                        cursor.execute('''
                            UPDATE weapons 
                            SET weapon_document = %s, updated_at = NOW()
                            WHERE id = %s
                        ''', (file_path, weapon_id))
                        
                        updated_count += 1
                        print(f"   ✅ تم ربط الملف {file} بالسلاح {serial_from_file}")
        
        conn.commit()
        print(f"\n🎉 تم تحديث {updated_count} سلاح بملفات السندات!")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في التحديث: {str(e)}")

if __name__ == '__main__':
    print("=" * 60)
    print("فحص تطابق ملفات السندات مع قاعدة البيانات")
    print("=" * 60)
    
    check_documents_sync()
    
    print("\n" + "=" * 60)
    response = input("هل تريد تحديث قاعدة البيانات لربط الملفات الموجودة؟ (y/n): ")
    
    if response.lower() in ['y', 'yes', 'نعم']:
        update_missing_documents()
        print("\n🔍 فحص مرة أخرى بعد التحديث...")
        check_documents_sync()
    else:
        print("تم إلغاء التحديث.")
