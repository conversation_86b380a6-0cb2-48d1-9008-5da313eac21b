#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إيقاف جدولة النسخ الاحتياطي السريع
Quick Schedule Stop

ملف سريع لإيقاف الجدولة النشطة
"""

import os
import sys

# إضافة المسار الحالي للبحث عن الوحدات
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def stop_active_schedules():
    """إيقاف جميع الجدولات النشطة"""
    
    print("⏸️  إيقاف جدولات النسخ الاحتياطي النشطة")
    print("=" * 50)
    
    try:
        from app import create_app
        from models import BackupSchedule
        from db import db
        
        app = create_app()
        with app.app_context():
            # البحث عن الجدولات النشطة
            active_schedules = BackupSchedule.query.filter_by(is_active=True).all()
            
            if not active_schedules:
                print("📋 لا توجد جدولات نشطة للإيقاف")
                return True
            
            print(f"📋 وجدت {len(active_schedules)} جدولة نشطة:")
            
            for schedule in active_schedules:
                schedule_type_ar = {
                    'daily': 'يومي',
                    'weekly': 'أسبوعي', 
                    'monthly': 'شهري'
                }.get(schedule.schedule_type, schedule.schedule_type)
                
                backup_type_ar = {
                    'full': 'كامل',
                    'warehouse': 'مستودع'
                }.get(schedule.backup_type, schedule.backup_type)
                
                warehouse_name = schedule.warehouse.name if schedule.warehouse else "جميع المستودعات"
                
                print(f"   - ID {schedule.id}: {schedule_type_ar} - {backup_type_ar} - {warehouse_name}")
            
            # تأكيد الإيقاف
            confirm = input(f"\n❓ هل تريد إيقاف جميع الجدولات النشطة؟ (y/N): ").strip().lower()
            
            if confirm in ['y', 'yes', 'نعم']:
                # إيقاف جميع الجدولات
                for schedule in active_schedules:
                    schedule.is_active = False
                    schedule.next_run = None
                
                db.session.commit()
                
                print(f"✅ تم إيقاف {len(active_schedules)} جدولة بنجاح")
                print("📋 لن يتم إنشاء نسخ احتياطية تلقائية حتى إعادة التفعيل")
                return True
            else:
                print("❌ تم إلغاء عملية الإيقاف")
                return False
            
    except Exception as e:
        print(f"❌ خطأ في إيقاف الجدولات: {str(e)}")
        return False

def stop_specific_schedule():
    """إيقاف جدولة محددة"""
    
    print("⏸️  إيقاف جدولة محددة")
    print("=" * 30)
    
    try:
        from app import create_app
        from models import BackupSchedule
        from db import db
        
        app = create_app()
        with app.app_context():
            # عرض الجدولات النشطة
            active_schedules = BackupSchedule.query.filter_by(is_active=True).all()
            
            if not active_schedules:
                print("📋 لا توجد جدولات نشطة للإيقاف")
                return True
            
            print("📋 الجدولات النشطة:")
            for schedule in active_schedules:
                schedule_type_ar = {
                    'daily': 'يومي',
                    'weekly': 'أسبوعي', 
                    'monthly': 'شهري'
                }.get(schedule.schedule_type, schedule.schedule_type)
                
                backup_type_ar = {
                    'full': 'كامل',
                    'warehouse': 'مستودع'
                }.get(schedule.backup_type, schedule.backup_type)
                
                warehouse_name = schedule.warehouse.name if schedule.warehouse else "جميع المستودعات"
                
                from utils import format_time_12h
                time_str = format_time_12h(schedule.hour, schedule.minute)
                
                print(f"   {schedule.id}. {schedule_type_ar} - {backup_type_ar} - {time_str} - {warehouse_name}")
            
            # اختيار الجدولة
            try:
                schedule_id = int(input(f"\nأدخل ID الجدولة للإيقاف (1-{len(active_schedules)}): ").strip())
                
                schedule = BackupSchedule.query.get(schedule_id)
                
                if not schedule:
                    print(f"❌ لا توجد جدولة بـ ID = {schedule_id}")
                    return False
                
                if not schedule.is_active:
                    print(f"⚠️  الجدولة {schedule_id} متوقفة بالفعل")
                    return True
                
                # إيقاف الجدولة
                schedule.is_active = False
                schedule.next_run = None
                
                db.session.commit()
                
                print(f"✅ تم إيقاف الجدولة {schedule_id} بنجاح")
                return True
                
            except ValueError:
                print("❌ يرجى إدخال رقم صحيح")
                return False
            
    except Exception as e:
        print(f"❌ خطأ في إيقاف الجدولة: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🛑 أداة إيقاف جدولة النسخ الاحتياطي")
    print("=" * 40)
    
    print("\nالخيارات:")
    print("1. إيقاف جميع الجدولات النشطة")
    print("2. إيقاف جدولة محددة")
    print("3. عرض الجدولات فقط")
    
    choice = input("\nاختر (1-3): ").strip()
    
    if choice == '1':
        stop_active_schedules()
        
    elif choice == '2':
        stop_specific_schedule()
        
    elif choice == '3':
        try:
            from app import create_app
            from models import BackupSchedule
            from utils import format_time_12h, format_datetime_12h
            
            app = create_app()
            with app.app_context():
                schedules = BackupSchedule.query.all()
                
                if not schedules:
                    print("📋 لا توجد جدولات في قاعدة البيانات")
                    return
                
                print(f"\n📋 جميع الجدولات ({len(schedules)}):")
                print("-" * 60)
                
                for schedule in schedules:
                    schedule_type_ar = {
                        'daily': 'يومي',
                        'weekly': 'أسبوعي', 
                        'monthly': 'شهري'
                    }.get(schedule.schedule_type, schedule.schedule_type)
                    
                    backup_type_ar = {
                        'full': 'كامل',
                        'warehouse': 'مستودع'
                    }.get(schedule.backup_type, schedule.backup_type)
                    
                    warehouse_name = schedule.warehouse.name if schedule.warehouse else "جميع المستودعات"
                    status = "🟢 نشط" if schedule.is_active else "🔴 متوقف"
                    
                    time_str = format_time_12h(schedule.hour, schedule.minute)
                    next_run = format_datetime_12h(schedule.next_run) if schedule.next_run else "-"
                    
                    print(f"ID {schedule.id}: {schedule_type_ar} - {backup_type_ar} - {time_str}")
                    print(f"        المستودع: {warehouse_name}")
                    print(f"        الحالة: {status}")
                    print(f"        التشغيل التالي: {next_run}")
                    print()
                    
        except Exception as e:
            print(f"❌ خطأ في عرض الجدولات: {str(e)}")
    
    else:
        print("❌ خيار غير صحيح")

if __name__ == "__main__":
    main()
