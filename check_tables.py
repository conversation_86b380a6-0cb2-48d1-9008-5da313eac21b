#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
فحص الجداول الموجودة في قاعدة البيانات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from locations import get_db_connection

def check_tables():
    """فحص الجداول الموجودة في قاعدة البيانات"""
    
    with app.app_context():
        print("🔍 فحص الجداول الموجودة في قاعدة البيانات...")
        print("=" * 50)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # جلب أسماء الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("📊 الجداول الموجودة:")
        for table in tables:
            table_name = table['name']
            print(f"  - {table_name}")
            
            # جلب عدد الصفوف في كل جدول
            try:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                count = cursor.fetchone()['count']
                print(f"    عدد الصفوف: {count}")
                
                # إذا كان الجدول يحتوي على بيانات، اعرض عينة
                if count > 0 and count < 10:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                    sample = cursor.fetchall()
                    print(f"    عينة من البيانات:")
                    for row in sample:
                        print(f"      {dict(row)}")
            except Exception as e:
                print(f"    خطأ في قراءة الجدول: {e}")
            print()
        
        conn.close()

if __name__ == "__main__":
    check_tables()
