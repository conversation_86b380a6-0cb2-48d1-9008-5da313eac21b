#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة عمود role_en وإصلاح عرض الأدوار
Add role_en column and fix role display

هذا الملف يقوم بإضافة عمود role_en لحفظ الأدوار بالإنجليزية
وإصلاح عرض صلاحية مناوب السرية
"""

import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from db import db
from models import User
from sqlalchemy import text

def add_role_en_column():
    """إضافة عمود role_en وإصلاح الأدوار"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 بدء إضافة عمود role_en...")
            
            # التحقق من وجود العمود
            try:
                result = db.session.execute(text("PRAGMA table_info(users)"))
                columns = [row[1] for row in result.fetchall()]
                
                if 'role_en' not in columns:
                    print("📝 إضافة عمود role_en...")
                    db.session.execute(text("ALTER TABLE users ADD COLUMN role_en VARCHAR(50)"))
                    db.session.commit()
                    print("✅ تم إضافة عمود role_en بنجاح")
                else:
                    print("✅ عمود role_en موجود بالفعل")
                    
            except Exception as e:
                print(f"⚠️  خطأ في إضافة العمود: {e}")
                db.session.rollback()
            
            # تحديث البيانات الموجودة
            print("\n🔄 تحديث البيانات الموجودة...")
            
            users = User.query.all()
            print(f"📊 عدد المستخدمين: {len(users)}")
            
            arabic_to_english = {
                'مدير نظام': 'admin',
                'مدير المستودعات': 'warehouse_manager',
                'مسؤول مخزون': 'inventory_manager',
                'مراقب': 'monitor',
                'مناوب السرية': 'company_duty'
            }
            
            updated_users = 0
            for user in users:
                print(f"\n👤 المستخدم: {user.username}")
                print(f"   - user_role: {user.user_role}")
                print(f"   - role_en (قبل): {getattr(user, 'role_en', 'غير محدد')}")
                
                # تحديد الدور الإنجليزي
                if user.user_role and user.user_role in arabic_to_english:
                    new_role_en = arabic_to_english[user.user_role]
                elif user.is_admin:
                    new_role_en = 'admin'
                    user.user_role = 'مدير نظام'
                else:
                    new_role_en = 'monitor'
                    if not user.user_role:
                        user.user_role = 'مراقب'
                
                # تحديث role_en
                user.role_en = new_role_en
                
                # التأكد من is_admin
                if new_role_en == 'admin':
                    user.is_admin = True
                else:
                    user.is_admin = False
                
                updated_users += 1
                print(f"   - role_en (بعد): {user.role_en}")
                print(f"   - user_role (بعد): {user.user_role}")
                print(f"   - is_admin: {user.is_admin}")
                print(f"   - role property: {user.role}")
                print(f"   - is_company_duty: {user.is_company_duty}")
            
            # حفظ التغييرات
            db.session.commit()
            print(f"\n✅ تم تحديث {updated_users} مستخدم بنجاح")
            
            # اختبار العرض النهائي
            print("\n🧪 اختبار العرض النهائي:")
            for user in users:
                display_role = ""
                if user.role == 'admin' or user.is_admin:
                    display_role = "مدير نظام"
                elif user.role == 'warehouse_manager':
                    display_role = "مدير مستودع"
                elif user.role == 'inventory_manager':
                    display_role = "مسؤول مخزون"
                elif user.role == 'company_duty' or user.user_role == 'مناوب السرية':
                    display_role = "مناوب السرية"
                else:
                    display_role = "مراقب"
                
                print(f"   - {user.username}: {display_role} (role: {user.role})")
            
            print("\n🎉 تم إصلاح عرض الأدوار بنجاح!")
            
        except Exception as e:
            print(f"❌ خطأ في إضافة العمود: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    add_role_en_column()
