#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
سكريبت لاختبار رفع سند السلاح
"""

import psycopg2
import os

def test_weapon_document_upload():
    """اختبار رفع سند السلاح"""
    
    try:
        # الاتصال بقاعدة البيانات
        conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
        cursor = conn.cursor()
        
        print("🔍 اختبار وظيفة رفع سند السلاح...")
        
        # البحث عن سلاح للاختبار
        cursor.execute('''
            SELECT id, name, serial_number, weapon_document 
            FROM weapons 
            WHERE serial_number LIKE %s
            LIMIT 1
        ''', ('%3480%',))
        
        weapon = cursor.fetchone()
        
        if weapon:
            weapon_id, name, serial, current_document = weapon
            print(f"✅ تم العثور على السلاح للاختبار:")
            print(f"   - المعرف: {weapon_id}")
            print(f"   - الاسم: {name}")
            print(f"   - الرقم التسلسلي: {serial}")
            print(f"   - السند الحالي: {current_document if current_document else 'غير موجود'}")
            
            # التحقق من وجود ملف السند
            if current_document:
                full_path = os.path.join('static', current_document.replace('static/', ''))
                if os.path.exists(full_path):
                    file_size = os.path.getsize(full_path)
                    print(f"   ✅ ملف السند موجود: {full_path}")
                    print(f"   📏 حجم الملف: {file_size:,} بايت ({file_size/1024/1024:.2f} ميجابايت)")
                else:
                    print(f"   ❌ ملف السند غير موجود في المسار: {full_path}")
            
            # التحقق من مجلد الرفع
            upload_dir = 'static/uploads/weapon_documents'
            if os.path.exists(upload_dir):
                files = os.listdir(upload_dir)
                weapon_files = [f for f in files if serial.replace('-', '').replace('.', '') in f.replace('-', '').replace('.', '')]
                print(f"   📁 ملفات السلاح في مجلد الرفع ({len(weapon_files)}):")
                for file in weapon_files:
                    file_path = os.path.join(upload_dir, file)
                    file_size = os.path.getsize(file_path)
                    print(f"     - {file} ({file_size:,} بايت)")
            else:
                print(f"   ❌ مجلد الرفع غير موجود: {upload_dir}")
        
        else:
            print("❌ لم يتم العثور على أسلحة للاختبار")
        
        # إحصائيات عامة
        print(f"\n📊 إحصائيات عامة:")
        
        cursor.execute('SELECT COUNT(*) FROM weapons WHERE weapon_document IS NOT NULL')
        weapons_with_docs = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM weapons')
        total_weapons = cursor.fetchone()[0]
        
        print(f"   - إجمالي الأسلحة: {total_weapons}")
        print(f"   - الأسلحة التي لها أسناد: {weapons_with_docs}")
        print(f"   - النسبة المئوية: {(weapons_with_docs/total_weapons*100):.1f}%")
        
        # عرض آخر 5 أسلحة تم رفع أسناد لها
        cursor.execute('''
            SELECT name, serial_number, weapon_document, updated_at
            FROM weapons 
            WHERE weapon_document IS NOT NULL
            ORDER BY updated_at DESC
            LIMIT 5
        ''')
        
        recent_docs = cursor.fetchall()
        if recent_docs:
            print(f"\n📋 آخر 5 أسلحة تم رفع أسناد لها:")
            for i, (name, serial, doc_path, updated_at) in enumerate(recent_docs, 1):
                print(f"   {i}. {name}")
                print(f"      الرقم التسلسلي: {serial}")
                print(f"      مسار السند: {doc_path}")
                print(f"      تاريخ التحديث: {updated_at}")
                print("      ---")
        
        # التحقق من مجلد الرفع العام
        upload_base_dir = 'static/uploads'
        if os.path.exists(upload_base_dir):
            subdirs = [d for d in os.listdir(upload_base_dir) if os.path.isdir(os.path.join(upload_base_dir, d))]
            print(f"\n📁 مجلدات الرفع المتاحة:")
            for subdir in subdirs:
                subdir_path = os.path.join(upload_base_dir, subdir)
                file_count = len([f for f in os.listdir(subdir_path) if os.path.isfile(os.path.join(subdir_path, f))])
                print(f"   - {subdir}: {file_count} ملف")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")

if __name__ == '__main__':
    test_weapon_document_upload()
