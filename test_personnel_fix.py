#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار إصلاح إضافة الأفراد
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from locations import add_personnel_to_location, get_db_connection

def test_personnel_fix():
    """اختبار إصلاح إضافة الأفراد"""
    
    with app.app_context():
        print("🔧 اختبار إصلاح إضافة الأفراد...")
        print("=" * 50)
        
        # معرفات للاختبار
        location_id = 48  # الكاجيما
        personnel_id = 41  # محمد بن خالد الزهراني
        
        print(f"📍 الموقع: {location_id}")
        print(f"👤 الفرد: {personnel_id}")
        
        # فحص الحالة الحالية
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT * FROM location_personnel 
            WHERE location_id = ? AND personnel_id = ?
        """, (location_id, personnel_id))
        
        existing_records = cursor.fetchall()
        print(f"\n📊 السجلات الموجودة: {len(existing_records)}")
        
        for record in existing_records:
            print(f"  - ID: {record['id']}, نشط: {record['is_active']}, تاريخ التعيين: {record['assignment_date']}")
        
        conn.close()
        
        # اختبار إضافة الفرد
        print(f"\n🔄 محاولة إضافة الفرد...")
        success, message = add_personnel_to_location(location_id, personnel_id, 'اختبار', 'اختبار الإصلاح')
        
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ {message}")
        
        # فحص الحالة بعد الإضافة
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT * FROM location_personnel 
            WHERE location_id = ? AND personnel_id = ?
            ORDER BY updated_at DESC
        """, (location_id, personnel_id))
        
        updated_records = cursor.fetchall()
        print(f"\n📊 السجلات بعد الإضافة: {len(updated_records)}")
        
        for record in updated_records:
            print(f"  - ID: {record['id']}, نشط: {record['is_active']}, تاريخ التعيين: {record['assignment_date']}")
            print(f"    نوع الوردية: {record['shift_type']}, ملاحظات: {record['notes']}")
        
        conn.close()
        
        # اختبار إضافة نفس الفرد مرة أخرى
        print(f"\n🔄 محاولة إضافة نفس الفرد مرة أخرى...")
        success2, message2 = add_personnel_to_location(location_id, personnel_id, 'اختبار2', 'اختبار مكرر')
        
        if success2:
            print(f"✅ {message2}")
        else:
            print(f"❌ {message2}")

if __name__ == "__main__":
    test_personnel_fix()
