#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إزالة المستودعات من مناوب السرية
Remove warehouses from Company Duty users

هذا الملف يقوم بإزالة جميع المستودعات من المستخدمين بصلاحية مناوب السرية
لأنهم لا يحتاجون إلى مستودعات (يعملون على كشف الاستلامات وإدارة المواقع فقط)
"""

import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from db import db
from models import User
from sqlalchemy import or_

def remove_warehouses_from_company_duty():
    """إزالة المستودعات من جميع مستخدمي مناوب السرية"""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("🔄 بدء إزالة المستودعات من مناوب السرية...")
            
            # البحث عن جميع مستخدمي مناوب السرية
            company_duty_users = User.query.filter(
                or_(User.role == 'company_duty', User.user_role == 'مناوب السرية')
            ).all()
            
            if not company_duty_users:
                print("ℹ️  لا يوجد مستخدمين بصلاحية مناوب السرية")
                return
            
            print(f"📊 تم العثور على {len(company_duty_users)} مستخدم بصلاحية مناوب السرية:")
            
            updated_users = 0
            for user in company_duty_users:
                print(f"   - {user.username} ({user.user_role})")
                
                # التحقق من وجود مستودعات مرتبطة
                if user.warehouses:
                    warehouse_count = len(user.warehouses)
                    print(f"     🗂️  المستودعات المرتبطة: {warehouse_count}")
                    
                    # إزالة جميع المستودعات
                    user.warehouses.clear()
                    updated_users += 1
                    print(f"     ✅ تم إزالة {warehouse_count} مستودع")
                else:
                    print(f"     ✅ لا توجد مستودعات مرتبطة (محدث بالفعل)")
            
            if updated_users > 0:
                # حفظ التغييرات
                db.session.commit()
                print(f"\n✅ تم تحديث {updated_users} مستخدم بنجاح")
                print("🎉 تم إزالة جميع المستودعات من مناوب السرية")
            else:
                print("\n✅ جميع مستخدمي مناوب السرية محدثين بالفعل")
            
            print("\n📋 ملخص:")
            print("   - مناوب السرية لا يحتاج إلى مستودعات")
            print("   - يعمل على كشف الاستلامات وإدارة المواقع فقط")
            print("   - لا توجد قيود مستودعات على عمله")
            
        except Exception as e:
            print(f"❌ خطأ في إزالة المستودعات: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    remove_warehouses_from_company_duty()
