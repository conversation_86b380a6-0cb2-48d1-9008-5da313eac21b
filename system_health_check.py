#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص صحة النظام
System Health Check

هذا الملف يقوم بفحص صحة النظام والتأكد من عمل جميع المكونات
"""

import sys
import os
import subprocess
import importlib
import psutil
from pathlib import Path

def check_python_version():
    """فحص إصدار Python"""
    print("🐍 فحص إصدار Python...")
    
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro} (مدعوم)")
        return True
    else:
        print(f"   ❌ Python {version.major}.{version.minor}.{version.micro} (غير مدعوم)")
        print("   📋 يتطلب Python 3.8 أو أحدث")
        return False

def check_required_packages():
    """فحص المكتبات المطلوبة"""
    print("\n📦 فحص المكتبات المطلوبة...")
    
    required_packages = [
        'flask',
        'flask_sqlalchemy',
        'flask_login',
        'flask_wtf',
        'psycopg2',
        'pandas',
        'qrcode',
        'hijri_converter',
        'xlsxwriter'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (مفقود)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n   📋 المكتبات المفقودة: {', '.join(missing_packages)}")
        print("   🔧 لتثبيتها: pip install -r requirements.txt")
        return False
    
    return True

def check_postgresql():
    """فحص PostgreSQL"""
    print("\n🗄️  فحص PostgreSQL...")
    
    try:
        # فحص تشغيل PostgreSQL
        result = subprocess.run(
            ["pg_isready", "-h", "localhost", "-p", "5432"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("   ✅ PostgreSQL يعمل")
            
            # فحص الاتصال بقاعدة البيانات
            try:
                import psycopg2
                conn = psycopg2.connect(
                    host="localhost",
                    database="military_warehouse",
                    user="postgres",
                    password="postgres",
                    port="5432"
                )
                conn.close()
                print("   ✅ الاتصال بقاعدة البيانات ناجح")
                return True
                
            except Exception as e:
                print(f"   ❌ فشل الاتصال بقاعدة البيانات: {str(e)}")
                return False
                
        else:
            print("   ❌ PostgreSQL لا يعمل")
            print("   🔧 تأكد من تشغيل خدمة PostgreSQL")
            return False
            
    except FileNotFoundError:
        print("   ❌ أدوات PostgreSQL غير مثبتة")
        print("   🔧 تأكد من تثبيت PostgreSQL وإضافته لـ PATH")
        return False
    except subprocess.TimeoutExpired:
        print("   ❌ انتهت مهلة الاتصال")
        return False

def check_database_tables():
    """فحص جداول قاعدة البيانات"""
    print("\n📊 فحص جداول قاعدة البيانات...")
    
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from app import create_app
        from db import db
        
        app = create_app()
        with app.app_context():
            # فحص الجداول المطلوبة
            required_tables = [
                'users',
                'warehouses',
                'weapons',
                'locations',
                'statuses',
                'receipts',
                'activity_logs'
            ]
            
            inspector = db.inspect(db.engine)
            existing_tables = inspector.get_table_names()
            
            missing_tables = []
            for table in required_tables:
                if table in existing_tables:
                    print(f"   ✅ {table}")
                else:
                    print(f"   ❌ {table} (مفقود)")
                    missing_tables.append(table)
            
            if missing_tables:
                print(f"\n   📋 الجداول المفقودة: {', '.join(missing_tables)}")
                print("   🔧 لإنشائها: python setup_system.py")
                return False
            
            return True
            
    except Exception as e:
        print(f"   ❌ خطأ في فحص الجداول: {str(e)}")
        return False

def check_system_resources():
    """فحص موارد النظام"""
    print("\n💻 فحص موارد النظام...")
    
    try:
        # فحص الذاكرة
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        memory_used_percent = memory.percent
        
        print(f"   💾 الذاكرة الإجمالية: {memory_gb:.1f} GB")
        print(f"   📊 الذاكرة المستخدمة: {memory_used_percent:.1f}%")
        
        if memory_gb < 2:
            print("   ⚠️  الذاكرة قليلة (يُنصح بـ 4GB أو أكثر)")
        elif memory_used_percent > 90:
            print("   ⚠️  استخدام الذاكرة مرتفع")
        else:
            print("   ✅ الذاكرة كافية")
        
        # فحص القرص الصلب
        disk = psutil.disk_usage('.')
        disk_gb = disk.total / (1024**3)
        disk_free_gb = disk.free / (1024**3)
        disk_used_percent = (disk.used / disk.total) * 100
        
        print(f"   💿 مساحة القرص الإجمالية: {disk_gb:.1f} GB")
        print(f"   📊 المساحة المستخدمة: {disk_used_percent:.1f}%")
        print(f"   🆓 المساحة المتاحة: {disk_free_gb:.1f} GB")
        
        if disk_free_gb < 5:
            print("   ⚠️  المساحة المتاحة قليلة")
        else:
            print("   ✅ مساحة القرص كافية")
        
        # فحص المعالج
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        print(f"   🔧 عدد المعالجات: {cpu_count}")
        print(f"   📊 استخدام المعالج: {cpu_percent:.1f}%")
        
        if cpu_percent > 80:
            print("   ⚠️  استخدام المعالج مرتفع")
        else:
            print("   ✅ أداء المعالج جيد")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص موارد النظام: {str(e)}")
        return False

def check_file_permissions():
    """فحص صلاحيات الملفات"""
    print("\n📁 فحص صلاحيات الملفات...")
    
    try:
        important_files = [
            'app.py',
            'config.py',
            'models.py',
            'requirements.txt'
        ]
        
        for file_name in important_files:
            file_path = Path(file_name)
            if file_path.exists():
                if os.access(file_path, os.R_OK):
                    print(f"   ✅ {file_name} (قابل للقراءة)")
                else:
                    print(f"   ❌ {file_name} (غير قابل للقراءة)")
                    return False
            else:
                print(f"   ❌ {file_name} (غير موجود)")
                return False
        
        # فحص مجلدات مهمة
        important_dirs = ['templates', 'static']
        for dir_name in important_dirs:
            dir_path = Path(dir_name)
            if dir_path.exists() and dir_path.is_dir():
                print(f"   ✅ {dir_name}/ (موجود)")
            else:
                print(f"   ❌ {dir_name}/ (غير موجود)")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص الملفات: {str(e)}")
        return False

def check_network_connectivity():
    """فحص الاتصال بالشبكة"""
    print("\n🌐 فحص الاتصال بالشبكة...")
    
    try:
        import socket
        
        # فحص المنفذ 5000 (Flask)
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('localhost', 5000))
        sock.close()
        
        if result == 0:
            print("   ⚠️  المنفذ 5000 مستخدم (قد يكون النظام يعمل بالفعل)")
        else:
            print("   ✅ المنفذ 5000 متاح")
        
        # فحص المنفذ 5432 (PostgreSQL)
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('localhost', 5432))
        sock.close()
        
        if result == 0:
            print("   ✅ PostgreSQL متاح على المنفذ 5432")
            return True
        else:
            print("   ❌ PostgreSQL غير متاح على المنفذ 5432")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ في فحص الشبكة: {str(e)}")
        return False

def main():
    """الدالة الرئيسية لفحص صحة النظام"""
    
    print("🔍 فحص صحة نظام عتاد")
    print("=" * 60)
    
    checks = [
        ("إصدار Python", check_python_version),
        ("المكتبات المطلوبة", check_required_packages),
        ("PostgreSQL", check_postgresql),
        ("جداول قاعدة البيانات", check_database_tables),
        ("موارد النظام", check_system_resources),
        ("صلاحيات الملفات", check_file_permissions),
        ("الاتصال بالشبكة", check_network_connectivity)
    ]
    
    passed_checks = 0
    total_checks = len(checks)
    
    for check_name, check_function in checks:
        try:
            if check_function():
                passed_checks += 1
        except Exception as e:
            print(f"   ❌ خطأ في فحص {check_name}: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الفحص: {passed_checks}/{total_checks} اختبار نجح")
    
    if passed_checks == total_checks:
        print("🎉 النظام جاهز للتشغيل!")
        return True
    else:
        print("⚠️  يوجد مشاكل تحتاج إلى حل قبل تشغيل النظام")
        print("\n🔧 خطوات الإصلاح المقترحة:")
        print("   1. تأكد من تثبيت Python 3.8+")
        print("   2. ثبت المكتبات: pip install -r requirements.txt")
        print("   3. تأكد من تشغيل PostgreSQL")
        print("   4. أنشئ قاعدة البيانات: python setup_system.py")
        print("   5. تحقق من صلاحيات الملفات")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف الفحص بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {str(e)}")
        sys.exit(1)
