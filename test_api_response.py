#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار استجابة API للبحث عن الأفراد
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from locations import search_personnel_for_location

def test_api_response():
    """اختبار استجابة API للبحث عن الأفراد"""
    
    with app.app_context():
        print("🔍 اختبار استجابة API للبحث عن الأفراد...")
        print("=" * 50)
        
        # اختبار البحث بالرقم الذي يظهر في السجلات
        search_term = "1063224012"
        location_id = 81
        
        print(f"🔍 البحث عن: {search_term}")
        print(f"📍 في الموقع: {location_id}")
        
        # استدعاء دالة البحث مباشرة
        results = search_personnel_for_location(location_id, search_term)
        
        print(f"📊 عدد النتائج: {len(results)}")
        
        if results:
            print("\n✅ النتائج:")
            for person in results:
                print(f"  - الاسم: {person.get('name', 'غير محدد')}")
                print(f"    الرقم العسكري: {person.get('personnel_id', 'غير محدد')}")
                print(f"    رقم الهوية: {person.get('phone', 'غير محدد')}")
                print(f"    الحالة: {person.get('status', 'غير محدد')}")
                print(f"    الرتبة: {person.get('rank', 'غير محدد')}")
                print()
        else:
            print("❌ لا توجد نتائج")
            
            # البحث في قاعدة البيانات مباشرة
            from locations import get_db_connection
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # البحث في جدول الأفراد
            cursor.execute("""
                SELECT * FROM personnel 
                WHERE phone = ? OR personnel_id = ?
            """, (search_term, search_term))
            
            personnel_results = cursor.fetchall()
            
            if personnel_results:
                print(f"\n🔧 وجدت {len(personnel_results)} أفراد في قاعدة البيانات:")
                for person in personnel_results:
                    print(f"  - {person['name']} (ID: {person['id']})")
                    print(f"    الرقم العسكري: {person['personnel_id']}")
                    print(f"    رقم الهوية: {person['phone']}")
                    print(f"    الحالة: {person['status']}")
                    print()
                    
                    # التحقق من ربط الفرد بالموقع
                    cursor.execute("""
                        SELECT * FROM location_personnel 
                        WHERE personnel_id = ? AND location_id = ? AND is_active = 1
                    """, (person['id'], location_id))
                    
                    location_link = cursor.fetchone()
                    if location_link:
                        print(f"    ✅ مرتبط بالموقع {location_id}")
                    else:
                        print(f"    ❌ غير مرتبط بالموقع {location_id}")
                        
                        # البحث عن المواقع المرتبطة
                        cursor.execute("""
                            SELECT l.id, l.name FROM location_personnel lp
                            JOIN locations l ON lp.location_id = l.id
                            WHERE lp.personnel_id = ? AND lp.is_active = 1
                        """, (person['id'],))
                        
                        linked_locations = cursor.fetchall()
                        if linked_locations:
                            print(f"    📍 مرتبط بالمواقع:")
                            for loc in linked_locations:
                                print(f"      - {loc['name']} (ID: {loc['id']})")
                        else:
                            print(f"    📍 غير مرتبط بأي موقع")
                    print()
            else:
                print("❌ لا يوجد أفراد بهذا الرقم في قاعدة البيانات")
            
            conn.close()

if __name__ == "__main__":
    test_api_response()
