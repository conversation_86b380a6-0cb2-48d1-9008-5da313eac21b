#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import os

def debug_weapon_document():
    """تشخيص مشكلة عدم ظهور سند السلاح"""
    
    try:
        # الاتصال بقاعدة البيانات
        conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
        cursor = conn.cursor()
        
        # السلاح المحدد من الصورة
        serial = 'G-K4S1B-3480-0.091001634'
        
        print("🔍 تشخيص مشكلة عدم ظهور سند السلاح")
        print("=" * 50)
        
        # 1. التحقق من السلاح في قاعدة البيانات
        cursor.execute('''
            SELECT id, serial_number, weapon_document, updated_at
            FROM weapons 
            WHERE serial_number = %s
        ''', (serial,))
        
        weapon = cursor.fetchone()
        if weapon:
            weapon_id, serial_num, doc_path, updated_at = weapon
            print(f"✅ السلاح موجود:")
            print(f"   ID: {weapon_id}")
            print(f"   الرقم التسلسلي: {serial_num}")
            print(f"   مسار السند: {doc_path if doc_path else 'لا يوجد'}")
            print(f"   آخر تحديث: {updated_at}")
            
            # 2. إذا لم يكن هناك سند، ابحث عن الملف وأضفه
            if not doc_path:
                print("\n❌ لا يوجد سند مرتبط - البحث عن الملف...")
                
                # البحث عن الملف في المجلد
                documents_dir = 'static/uploads/weapon_documents'
                if os.path.exists(documents_dir):
                    files = os.listdir(documents_dir)
                    matching_files = [f for f in files if '3480' in f and 'G-K4S1B' in f]
                    
                    if matching_files:
                        print(f"✅ تم العثور على {len(matching_files)} ملف مطابق:")
                        for file in matching_files:
                            print(f"   - {file}")
                        
                        # ربط أول ملف مطابق
                        first_file = matching_files[0]
                        file_path = f"static/uploads/weapon_documents/{first_file}"
                        
                        cursor.execute('''
                            UPDATE weapons 
                            SET weapon_document = %s, updated_at = NOW()
                            WHERE id = %s
                        ''', (file_path, weapon_id))
                        
                        conn.commit()
                        print(f"✅ تم ربط الملف {first_file} بالسلاح")
                        
                        # التحقق من النتيجة
                        cursor.execute('SELECT weapon_document FROM weapons WHERE id = %s', (weapon_id,))
                        new_doc = cursor.fetchone()[0]
                        print(f"✅ السند الجديد: {new_doc}")
                    else:
                        print("❌ لم يتم العثور على ملفات مطابقة")
                else:
                    print(f"❌ مجلد السندات غير موجود: {documents_dir}")
            else:
                print(f"\n✅ السند مرتبط بالفعل: {doc_path}")
                
                # التحقق من وجود الملف
                if os.path.exists(doc_path):
                    file_size = os.path.getsize(doc_path)
                    print(f"✅ الملف موجود - الحجم: {file_size:,} بايت")
                else:
                    print(f"❌ الملف غير موجود: {doc_path}")
                    
                    # البحث عن ملف بديل
                    documents_dir = 'static/uploads/weapon_documents'
                    if os.path.exists(documents_dir):
                        files = os.listdir(documents_dir)
                        matching_files = [f for f in files if '3480' in f and 'G-K4S1B' in f]
                        
                        if matching_files:
                            print(f"🔄 تم العثور على ملف بديل: {matching_files[0]}")
                            new_file_path = f"static/uploads/weapon_documents/{matching_files[0]}"
                            
                            cursor.execute('''
                                UPDATE weapons 
                                SET weapon_document = %s, updated_at = NOW()
                                WHERE id = %s
                            ''', (new_file_path, weapon_id))
                            
                            conn.commit()
                            print(f"✅ تم تحديث مسار السند إلى: {new_file_path}")
        else:
            print(f"❌ لم يتم العثور على السلاح: {serial}")
        
        # 3. التحقق من قالب HTML
        print(f"\n🔍 فحص قالب HTML:")
        template_path = 'templates/weapons/details.html'
        if os.path.exists(template_path):
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # البحث عن كود عرض السند
            if 'weapon.weapon_document' in content:
                print("✅ كود عرض السند موجود في القالب")
                
                # استخراج الجزء المتعلق بالسند
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'weapon.weapon_document' in line:
                        print(f"   السطر {i+1}: {line.strip()}")
                        # عرض السطور المحيطة
                        for j in range(max(0, i-2), min(len(lines), i+5)):
                            if j != i:
                                print(f"   السطر {j+1}: {lines[j].strip()}")
                        break
            else:
                print("❌ كود عرض السند غير موجود في القالب")
        else:
            print(f"❌ ملف القالب غير موجود: {template_path}")
        
        # 4. إحصائيات عامة
        print(f"\n📊 إحصائيات:")
        cursor.execute('SELECT COUNT(*) FROM weapons WHERE weapon_document IS NOT NULL')
        weapons_with_docs = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM weapons')
        total_weapons = cursor.fetchone()[0]
        
        print(f"   - الأسلحة التي لها سندات: {weapons_with_docs}/{total_weapons}")
        
        conn.close()
        
        print(f"\n💡 الحلول المقترحة:")
        print(f"   1. أعد تشغيل التطبيق")
        print(f"   2. امسح كاش المتصفح (Ctrl+F5)")
        print(f"   3. تحقق من أن المستخدم له صلاحية الوصول")
        print(f"   4. تأكد من أن Flask يقرأ البيانات الجديدة")
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")

if __name__ == '__main__':
    debug_weapon_document()
