#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
البحث عن المواقع التي تحتوي على أفراد
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from locations import get_all_locations

def find_locations_with_personnel():
    """البحث عن المواقع التي تحتوي على أفراد"""
    
    with app.app_context():
        print("🔍 البحث عن المواقع التي تحتوي على أفراد...")
        print("=" * 50)
        
        locations = get_all_locations()
        
        locations_with_personnel = []
        for location in locations:
            if location.get('personnel_count', 0) > 0:
                locations_with_personnel.append(location)
        
        print(f"📊 إجمالي المواقع: {len(locations)}")
        print(f"👥 المواقع التي تحتوي على أفراد: {len(locations_with_personnel)}")
        
        if locations_with_personnel:
            print("\n📍 المواقع التي تحتوي على أفراد:")
            for location in locations_with_personnel:
                print(f"  - {location['name']} (ID: {location['id']}) - {location['personnel_count']} فرد")
                print(f"    الرابط: http://127.0.0.1:5000/locations/{location['id']}")
                print()
        else:
            print("❌ لا توجد مواقع تحتوي على أفراد")
            
            # البحث في قاعدة البيانات مباشرة
            from locations import get_db_connection
            conn = get_db_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT l.id, l.name, COUNT(lp.personnel_id) as personnel_count
                FROM locations l
                LEFT JOIN location_personnel lp ON l.id = lp.location_id AND lp.is_active = 1
                GROUP BY l.id, l.name
                HAVING personnel_count > 0
                ORDER BY personnel_count DESC
            """)
            
            db_locations = cursor.fetchall()
            conn.close()
            
            if db_locations:
                print(f"\n🔧 وجدت {len(db_locations)} مواقع في قاعدة البيانات:")
                for location in db_locations:
                    print(f"  - {location['name']} (ID: {location['id']}) - {location['personnel_count']} فرد")
                    print(f"    الرابط: http://127.0.0.1:5000/locations/{location['id']}")
                print("\n🔧 المشكلة في دالة get_all_locations")

if __name__ == "__main__":
    find_locations_with_personnel()
