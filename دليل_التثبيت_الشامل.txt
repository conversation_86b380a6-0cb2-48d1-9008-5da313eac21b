# دليل التثبيت الشامل لنظام عتاد

## 📋 نظرة عامة
هذا الدليل يشرح كيفية تثبيت وتشغيل نظام عتاد على جهاز جديد من الصفر.

---

## 🔧 المتطلبات الأساسية

### 1. نظام التشغيل
- **Windows 10/11** (مُختبر)
- **Linux Ubuntu 20.04+** (مدعوم)
- **macOS** (مدعوم)

### 2. البرامج المطلوبة
- **Python 3.8+**
- **PostgreSQL 13+**
- **Git** (اختياري)
- **محرر نصوص** (VS Code مُوصى به)

---

## 📦 الخطوة 1: تثبيت Python

### على Windows:
1. اذهب إلى [python.org](https://www.python.org/downloads/)
2. حمل أحدث إصدار Python 3.x
3. شغل الملف المحمل
4. **مهم**: تأكد من تفعيل "Add Python to PATH"
5. اختر "Install Now"

### التحقق من التثبيت:
```bash
python --version
pip --version
```

---

## 🗄️ الخطوة 2: تثبيت PostgreSQL

### على Windows:

#### أ. تحميل PostgreSQL:
1. اذهب إلى [postgresql.org](https://www.postgresql.org/download/windows/)
2. حمل PostgreSQL 15 أو أحدث
3. شغل ملف التثبيت

#### ب. عملية التثبيت:
1. اختر مجلد التثبيت (افتراضي: `C:\Program Files\PostgreSQL\15`)
2. اختر المكونات:
   - ✅ PostgreSQL Server
   - ✅ pgAdmin 4
   - ✅ Stack Builder
   - ✅ Command Line Tools
3. اختر مجلد البيانات (افتراضي: `C:\Program Files\PostgreSQL\15\data`)
4. **مهم**: اختر كلمة مرور للمستخدم `postgres` (احفظها!)
5. اختر المنفذ: `5432` (افتراضي)
6. اختر اللغة: `Default locale`
7. انتظر انتهاء التثبيت

#### ج. التحقق من التثبيت:
1. افتح Command Prompt كمدير
2. اكتب:
```bash
psql --version
```

### إعداد متغيرات البيئة (إذا لم تعمل الأوامر):
1. اذهب إلى Control Panel → System → Advanced System Settings
2. اضغط "Environment Variables"
3. في "System Variables" ابحث عن "Path"
4. أضف: `C:\Program Files\PostgreSQL\15\bin`

---

## 📁 الخطوة 3: تحضير ملفات النظام

### أ. إنشاء مجلد المشروع:
```bash
mkdir C:\iMQS
cd C:\iMQS
```

### ب. نسخ ملفات النظام:
انسخ جميع ملفات النظام إلى مجلد `C:\iMQS`

### ج. التحقق من الملفات المطلوبة:
```
C:\iMQS\
├── app.py
├── config.py
├── models.py
├── auth.py
├── requirements.txt
├── templates/
├── static/
└── ... (باقي الملفات)
```

---

## 🐍 الخطوة 4: إعداد البيئة الافتراضية

### إنشاء البيئة الافتراضية:
```bash
cd C:\iMQS
python -m venv venv
```

### تفعيل البيئة الافتراضية:

**على Windows:**
```bash
venv\Scripts\activate
```

**على Linux/Mac:**
```bash
source venv/bin/activate
```

### التحقق من التفعيل:
يجب أن ترى `(venv)` في بداية سطر الأوامر

---

## 📚 الخطوة 5: تثبيت المكتبات المطلوبة

### تثبيت المكتبات:
```bash
pip install -r requirements.txt
```

### إذا لم يوجد ملف requirements.txt، ثبت المكتبات يدوياً:
```bash
pip install Flask==2.3.3
pip install Flask-Login==0.6.3
pip install Flask-WTF==1.1.1
pip install WTForms==3.0.1
pip install SQLAlchemy==2.0.21
pip install Flask-SQLAlchemy==3.0.5
pip install psycopg2-binary==2.9.7
pip install Werkzeug==2.3.7
pip install pandas==2.1.1
pip install openpyxl==3.1.2
pip install xlsxwriter==3.1.9
pip install qrcode==7.4.2
pip install Pillow==10.0.1
pip install python-barcode==0.15.1
pip install hijri-converter==2.3.1
pip install pytz==2023.3
```

---

## 🗃️ الخطوة 6: إعداد قاعدة البيانات

### أ. إنشاء قاعدة البيانات:

#### فتح pgAdmin:
1. ابحث عن "pgAdmin" في قائمة Start
2. شغل pgAdmin 4
3. أدخل كلمة مرور `postgres` التي اخترتها

#### إنشاء قاعدة البيانات:
1. في pgAdmin، انقر بالزر الأيمن على "Databases"
2. اختر "Create" → "Database"
3. اسم قاعدة البيانات: `military_warehouse`
4. اضغط "Save"

### ب. إعداد الاتصال:

#### تحرير ملف config.py:
```python
SQLALCHEMY_DATABASE_URI = "postgresql://postgres:كلمة_المرور_هنا@localhost:5432/military_warehouse"
```

**مثال:**
```python
SQLALCHEMY_DATABASE_URI = "postgresql://postgres:123456@localhost:5432/military_warehouse"
```

---

## 🚀 الخطوة 7: تشغيل النظام لأول مرة

### أ. إنشاء الجداول:
```bash
cd C:\iMQS
python -c "from app import create_app; from db import db; app = create_app(); app.app_context().push(); db.create_all(); print('تم إنشاء الجداول بنجاح!')"
```

### ب. تشغيل النظام:
```bash
python app.py
```

### ج. فتح النظام في المتصفح:
اذهب إلى: `http://localhost:5000`

---

## 👤 الخطوة 8: تسجيل الدخول الأول

### بيانات المدير الافتراضي:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

### تغيير كلمة المرور:
1. سجل دخول بالبيانات الافتراضية
2. اذهب إلى الإعدادات → تغيير كلمة المرور
3. أدخل كلمة مرور قوية جديدة

---

## 🔧 الخطوة 9: إعداد البيانات الأساسية

### أ. إضافة المستودعات:
النظام ينشئ 3 مستودعات افتراضية:
- المستودع الأول
- المستودع الثاني  
- المخزون العام

### ب. إضافة المستخدمين:
1. اذهب إلى "المستخدمين" → "إضافة مستخدم جديد"
2. أضف المستخدمين حسب الحاجة:
   - **مدير نظام**: صلاحية كاملة
   - **مدير المستودعات**: إدارة المستودعات
   - **مسؤول مخزون**: إدارة المخزون
   - **مراقب**: عرض فقط
   - **مناوب السرية**: كشف الاستلامات والمواقع فقط

---

## 🛠️ استكشاف الأخطاء وحلها

### مشكلة: "psycopg2 not found"
**الحل:**
```bash
pip install psycopg2-binary
```

### مشكلة: "connection refused"
**الحل:**
1. تأكد من تشغيل PostgreSQL:
   - Windows: Services → PostgreSQL
2. تحقق من المنفذ 5432
3. تحقق من كلمة المرور في config.py

### مشكلة: "table does not exist"
**الحل:**
```bash
python -c "from app import create_app; from db import db; app = create_app(); app.app_context().push(); db.create_all()"
```

### مشكلة: "Port 5000 already in use"
**الحل:**
غير المنفذ في app.py:
```python
if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
```

---

## 🔄 تشغيل النظام يومياً

### 1. تفعيل البيئة الافتراضية:
```bash
cd C:\iMQS
venv\Scripts\activate
```

### 2. تشغيل النظام:
```bash
python app.py
```

### 3. فتح المتصفح:
`http://localhost:5000`

---

## 💾 النسخ الاحتياطي

### نسخ احتياطي لقاعدة البيانات:
```bash
pg_dump -U postgres -h localhost military_warehouse > backup.sql
```

### استعادة النسخة الاحتياطية:
```bash
psql -U postgres -h localhost military_warehouse < backup.sql
```

---

## 📞 الدعم الفني

### في حالة وجود مشاكل:
1. تحقق من ملف `app.log` للأخطاء
2. تأكد من تشغيل PostgreSQL
3. تحقق من صحة بيانات الاتصال في config.py
4. تأكد من تفعيل البيئة الافتراضية

### ملفات مهمة للمراجعة:
- `config.py` - إعدادات النظام
- `app.py` - الملف الرئيسي
- `models.py` - نماذج قاعدة البيانات

---

## ✅ قائمة التحقق النهائية

- [ ] Python مثبت ويعمل
- [ ] PostgreSQL مثبت ويعمل
- [ ] قاعدة البيانات `military_warehouse` منشأة
- [ ] البيئة الافتراضية مفعلة
- [ ] جميع المكتبات مثبتة
- [ ] الجداول منشأة في قاعدة البيانات
- [ ] النظام يعمل على `http://localhost:5000`
- [ ] تم تسجيل الدخول بنجاح
- [ ] تم تغيير كلمة مرور المدير

🎉 **مبروك! النظام جاهز للاستخدام**

---

## 🔧 الصيانة والإدارة

### تحديث النظام:
```bash
# تفعيل البيئة الافتراضية
venv\Scripts\activate

# تحديث المكتبات
pip install --upgrade -r requirements.txt

# إعادة تشغيل النظام
python app.py
```

### مراقبة الأداء:
- راقب استخدام الذاكرة والمعالج
- تحقق من مساحة القرص الصلب
- راجع ملفات السجلات بانتظام

### تنظيف قاعدة البيانات:
```sql
-- حذف السجلات القديمة (أكثر من سنة)
DELETE FROM activity_logs WHERE created_at < NOW() - INTERVAL '1 year';

-- إعادة فهرسة الجداول
REINDEX DATABASE military_warehouse;
```

---

## 🔒 الأمان والحماية

### تأمين قاعدة البيانات:
1. غير كلمة مرور PostgreSQL الافتراضية
2. قيد الوصول لقاعدة البيانات على الشبكة المحلية فقط
3. فعل SSL للاتصالات

### تأمين التطبيق:
1. غير المفتاح السري في config.py
2. استخدم HTTPS في الإنتاج
3. قيد الوصول بجدار الحماية

### مراقبة الأمان:
- راجع سجلات تسجيل الدخول
- راقب المحاولات المشبوهة
- حدث كلمات المرور بانتظام

---

## 📊 مراقبة النظام

### ملفات السجلات:
- `app.log` - سجلات التطبيق
- `error.log` - سجلات الأخطاء
- PostgreSQL logs - سجلات قاعدة البيانات

### مؤشرات الأداء:
- عدد المستخدمين المتصلين
- استجابة قاعدة البيانات
- استخدام الذاكرة

---

## 🚀 النشر في الإنتاج

### متطلبات الخادم:
- **المعالج**: 4 cores أو أكثر
- **الذاكرة**: 8GB RAM أو أكثر
- **التخزين**: 100GB SSD أو أكثر
- **الشبكة**: اتصال مستقر

### إعداد الإنتاج:
```python
# في config.py
DEBUG = False
SECRET_KEY = 'مفتاح-سري-قوي-جداً'
SQLALCHEMY_DATABASE_URI = '**********************************/db'
```

### خادم الويب:
استخدم Gunicorn أو uWSGI مع Nginx:
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

---

## 📞 جهات الاتصال

### الدعم الفني:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966-11-XXXXXXX
- **ساعات العمل**: 8:00 - 16:00 (الأحد - الخميس)

### التطوير والتحديث:
- **فريق التطوير**: <EMAIL>
- **طلبات التحسين**: <EMAIL>

---

## 📚 مصادر إضافية

### الوثائق التقنية:
- [Flask Documentation](https://flask.palletsprojects.com/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/)

### أدوات مفيدة:
- **pgAdmin**: إدارة قاعدة البيانات
- **VS Code**: تطوير وتحرير الكود
- **Postman**: اختبار APIs

---

## 🎯 خارطة الطريق

### الإصدارات القادمة:
- [ ] تطبيق الهاتف المحمول
- [ ] تكامل مع أنظمة أخرى
- [ ] تقارير متقدمة
- [ ] ذكاء اصطناعي للتنبؤات

### التحسينات المخططة:
- [ ] واجهة مستخدم محسنة
- [ ] أداء أفضل
- [ ] ميزات أمان إضافية
- [ ] دعم لغات متعددة

---

## ✅ نهاية الدليل

تم إنشاء هذا الدليل لضمان تثبيت وتشغيل النظام بنجاح. في حالة وجود أي استفسارات أو مشاكل، لا تتردد في التواصل مع فريق الدعم الفني.

**تاريخ آخر تحديث**: ديسمبر 2024
**إصدار الدليل**: 1.0
