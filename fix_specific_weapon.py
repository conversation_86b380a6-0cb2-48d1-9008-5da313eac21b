#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import psycopg2
import os

def fix_specific_weapon():
    # الاتصال بقاعدة البيانات
    conn = psycopg2.connect('postgresql://postgres:postgres@localhost:5432/military_warehouse')
    cursor = conn.cursor()

    # السلاح المحدد
    serial = 'G-K4S1B-3480-0.091001634'

    # البحث عن الملف في المجلد
    documents_dir = 'static/uploads/weapon_documents'
    files = os.listdir(documents_dir)
    matching_files = [f for f in files if '3480' in f and 'G-K4S1B' in f]

    print(f'الملفات المطابقة للسلاح {serial}:')
    for file in matching_files:
        print(f'  - {file}')

    if matching_files:
        # استخدام أول ملف مطابق
        file_path = f'static/uploads/weapon_documents/{matching_files[0]}'
        
        # تحديث قاعدة البيانات
        cursor.execute('UPDATE weapons SET weapon_document = %s WHERE serial_number = %s', (file_path, serial))
        conn.commit()
        
        print(f'تم ربط الملف {matching_files[0]} بالسلاح {serial}')
        
        # التحقق من النتيجة
        cursor.execute('SELECT weapon_document FROM weapons WHERE serial_number = %s', (serial,))
        result = cursor.fetchone()
        print(f'السند المرتبط الآن: {result[0] if result else "لا يوجد"}')
        
        # التحقق من وجود الملف
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f'الملف موجود - الحجم: {file_size:,} بايت')
        else:
            print('الملف غير موجود!')
    else:
        print('لم يتم العثور على ملفات مطابقة')

    conn.close()

if __name__ == '__main__':
    fix_specific_weapon()
