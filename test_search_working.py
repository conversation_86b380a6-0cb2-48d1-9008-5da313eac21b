#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
اختبار البحث عن الأفراد
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import Personnel

def test_search_working():
    """اختبار البحث عن الأفراد"""
    
    with app.app_context():
        print("🔍 اختبار البحث عن الأفراد...")
        print("=" * 50)
        
        # جلب جميع الأفراد
        all_personnel = Personnel.query.all()
        print(f"👥 إجمالي الأفراد: {len(all_personnel)}")
        
        print("\n📋 قائمة الأفراد:")
        for person in all_personnel:
            print(f"  - {person.name}")
            print(f"    الرقم العسكري: {person.personnel_id}")
            print(f"    رقم الهوية: {person.phone}")
            print(f"    ID: {person.id}")
            print()
        
        # اختبار البحث
        search_terms = ["1063224012", "52524299", "1063224011"]
        
        for search_term in search_terms:
            print(f"🔍 البحث عن: {search_term}")
            
            # البحث بالرقم العسكري أو رقم الهوية
            results = Personnel.query.filter(
                (Personnel.phone == search_term) | 
                (Personnel.personnel_id == search_term)
            ).all()
            
            if results:
                print(f"  ✅ وجدت {len(results)} نتيجة:")
                for person in results:
                    print(f"    - {person.name} (ID: {person.id})")
                    print(f"      الرقم العسكري: {person.personnel_id}")
                    print(f"      رقم الهوية: {person.phone}")
            else:
                print(f"  ❌ لا توجد نتائج")
            print()

if __name__ == "__main__":
    test_search_working()
